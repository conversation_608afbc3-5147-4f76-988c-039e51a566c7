# K230 最简单的摄像头LCD显示测试
# Copyright: Mi Cu Electronics Studio
# Developer: <PERSON> (Mike Team Engineer)
# 完全基于您的工作参考代码 - 最简化版本

import time
import os
import sys
import gc

from media.sensor import *
from media.display import *
from media.media import *

# 显示配置参数 - 完全基于工作参考代码
DETECT_WIDTH = ALIGN_UP(640, 16)
DETECT_HEIGHT = 480

def simple_camera_test():
    """最简单的摄像头LCD显示测试"""
    print("🎯 K230 Simple Camera LCD Test")
    print("🏭 Based on Your Working Reference Code")
    
    sensor = None
    
    try:
        # 启用退出点 - 基于工作参考代码
        os.exitpoint(os.EXITPOINT_ENABLE)
        
        print("🔧 Step 1: Initializing camera...")
        
        # 初始化摄像头 - 完全基于工作参考代码
        sensor = Sensor(width=DETECT_WIDTH, height=DETECT_HEIGHT)
        sensor.reset()
        sensor.set_framesize(width=DETECT_WIDTH, height=DETECT_HEIGHT)
        sensor.set_pixformat(Sensor.RGB565)
        print(f"✅ Camera OK: {DETECT_WIDTH}x{DETECT_HEIGHT}")

        print("🖥️ Step 2: Initializing LCD display...")
        
        # 初始化显示 - 完全基于工作参考代码
        Display.init(Display.ST7701, to_ide=True)
        print("✅ LCD Display OK (ST7701)")

        print("📱 Step 3: Initializing media manager...")
        
        # 初始化媒体管理器 - 基于工作参考代码
        MediaManager.init()
        print("✅ Media Manager OK")
        
        # 启动摄像头 - 基于工作参考代码
        sensor.run()
        print("✅ Camera Started")

        print("🎯 System Ready - Testing camera display...")
        print("⌨️ Press Ctrl+C to stop")
        
        # FPS计数器
        fps_counter = time.clock()
        frame_count = 0

        while True:
            fps_counter.tick()
            os.exitpoint()
            frame_count += 1

            try:
                # 获取摄像头图像 - 基于工作参考代码
                img = sensor.snapshot()
                
                # 测试：在图像上绘制简单信息 (不清除图像内容)
                img.draw_string_advanced(10, 10, 24, "Camera Test", color=(255, 255, 0))
                img.draw_string_advanced(10, 40, 20, f"Frame: {frame_count}", color=(0, 255, 255))
                
                # 测试：绘制一个简单的矩形
                img.draw_rectangle(50, 50, 100, 80, color=(255, 0, 0), thickness=3, fill=False)
                
                # 显示图像到LCD屏幕 - 基于工作参考代码
                Display.show_image(img)
                
                # 内存管理 - 基于工作参考代码
                gc.collect()
                
                # 每50帧打印一次状态
                if frame_count % 50 == 0:
                    print(f"📊 Frame {frame_count}: Camera working, LCD should show image")
                
            except Exception as e:
                print(f"⚠️ Frame error: {e}")
                time.sleep_ms(100)
                continue

    except KeyboardInterrupt:
        print("\n🛑 Test stopped")
    except Exception as e:
        print(f"❌ Error: {e}")
        import sys
        sys.print_exception(e)
    finally:
        # 清理资源 - 基于工作参考代码
        print("🧹 Cleaning up...")
        try:
            if sensor:
                sensor.stop()
                print("✅ Camera stopped")
            Display.deinit()
            print("✅ Display stopped")
            os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
            time.sleep_ms(100)
            MediaManager.deinit()
            print("✅ Media manager stopped")
        except Exception as e:
            print(f"⚠️ Cleanup error: {e}")
        print("✅ Test completed")

if __name__ == "__main__":
    simple_camera_test()
