# K230校准工具GPIO53按键控制更新说明

## 📋 文档信息
- **版本**: v1.1
- **更新时间**: 2025-07-30
- **负责人**: <PERSON> (Engineer)
- **版权归属**: 米醋电子工作室
- **更新内容**: 将"按任意键开始"改为GPIO53按键控制

## 🔧 更新内容

### 1. **按键控制方式变更**

#### **原始方式 (已废弃)**
```python
# 原始代码 - 不适用于K230嵌入式环境
print("按任意键开始校准...")
input()  # 需要键盘输入，K230无键盘
calibration_mode()
```

#### **新方式 (GPIO53按键)**
```python
# 新代码 - 使用GPIO53物理按键
print("🔘 准备开始校准，请按下GPIO53按键...")
calibration_mode()  # 内部包含按键等待逻辑
```

### 2. **GPIO53按键实现细节**

#### **硬件配置**
```python
# 按键配置 - 参考立创示例
CALIBRATION_BUTTON_PIN = 53  # 用户按键引脚 Bank4_GPIO53
CALIBRATION_GPIO_PIN = 53    # GPIO引脚号

# 导入必要的库
from machine import Pin
from machine import FPIOA
```

#### **按键初始化函数**
```python
def init_calibration_button():
    """初始化校准按键 - 参考立创示例"""
    global calibration_button
    try:
        # 第一步：创建FPIOA对象并分配引脚功能
        fpioa = FPIOA()
        fpioa.set_function(CALIBRATION_BUTTON_PIN, FPIOA.GPIO53)
        print(f"📌 FPIOA: Pin {CALIBRATION_BUTTON_PIN} -> GPIO53")

        # 第二步：初始化按键 - 立创示例使用PULL_DOWN，按下时高电平
        calibration_button = Pin(CALIBRATION_GPIO_PIN, Pin.IN, Pin.PULL_DOWN)
        print(f"🔘 Calibration button initialized: Pin {CALIBRATION_BUTTON_PIN} (Bank4_GPIO53)")
        print("🔍 Button mode: PULL_DOWN (0=released, 1=pressed)")

        return True
    except Exception as e:
        print(f"❌ Calibration button init failed: {e}")
        calibration_button = None
        return False
```

#### **按键检测函数**
```python
def wait_for_button_press():
    """等待按键按下 - 替代input()"""
    print("🔘 等待按下GPIO53按键开始校准...")
    print("🔍 按键状态检测中...")
    
    # 初始化按键
    if not init_calibration_button():
        print("❌ 按键初始化失败，使用延时替代")
        time.sleep(3)
        return True
    
    # 等待按键按下
    last_state = 0
    while True:
        current_state = calibration_button.value() if calibration_button else 0
        
        # 检测上升沿（按键按下）
        if current_state == 1 and last_state == 0:
            print("🔘 按键按下！开始校准...")
            time.sleep_ms(200)  # 消抖延时
            return True
            
        last_state = current_state
        time.sleep_ms(50)  # 检测间隔
```

### 3. **参数同步更新**

#### **校准工具参数更新**
```python
# k230_distance_calibration_tool.py
KNOWN_WIDTH = 10.31      # 用户已校准值 (原8.27)
FOCAL_LENGTH = 551.8902  # 用户已校准值 (原800)
```

#### **主程序参数同步**
```python
# k230_distance_measurement.py
KNOWN_WIDTH = 10.31      # 同步用户校准值
FOCAL_LENGTH = 551.8902  # 同步用户校准值
```

## 🎯 使用方法

### 1. **启动校准工具**
```python
# 在K230上运行
exec(open('k230_distance_calibration_tool.py').read())
```

### 2. **操作流程**
1. **系统启动**: 显示校准指南和当前参数
2. **等待按键**: 系统提示"等待按下GPIO53按键开始校准..."
3. **按下GPIO53**: 物理按下Bank4_GPIO53按键
4. **开始校准**: 系统初始化摄像头和显示，进入校准模式
5. **实时校准**: 显示像素宽度、距离计算和校准公式

### 3. **按键位置**
- **引脚**: Bank4_GPIO53 (Pin 53)
- **模式**: PULL_DOWN (按下时高电平)
- **检测**: 上升沿触发 (0→1)
- **消抖**: 200ms延时

## 🔍 技术细节

### 按键检测原理
```python
# 上升沿检测逻辑
if current_state == 1 and last_state == 0:  # 从0变为1
    # 按键按下事件
    return True
```

### 错误处理
```python
# 如果按键初始化失败，使用延时替代
if not init_calibration_button():
    print("❌ 按键初始化失败，使用延时替代")
    time.sleep(3)  # 3秒后自动开始
    return True
```

### 状态显示
```python
# 在校准界面显示按键提示
img.draw_string_advanced(10, PICTURE_HEIGHT - 20, 14, 
                        "Press GPIO53 to start calibration", 
                        color=(255, 255, 0))
```

## 📊 更新对比

| 特性 | 原始版本 | GPIO53版本 |
|------|----------|------------|
| 启动方式 | 键盘input() | GPIO53按键 |
| 适用环境 | PC开发环境 | K230嵌入式 |
| 用户体验 | 需要键盘 | 物理按键 |
| 错误处理 | 无 | 自动降级 |
| 状态反馈 | 文本提示 | 屏幕+文本 |
| 参数精度 | 默认值 | 用户校准值 |

## 🎯 优势

### 1. **嵌入式友好**
- 无需外接键盘
- 使用板载按键
- 完全独立运行

### 2. **用户体验优化**
- 物理按键操作直观
- 实时状态显示
- 错误自动处理

### 3. **参数精确**
- 使用用户已校准的精确参数
- KNOWN_WIDTH = 10.31 (vs 标准8.27)
- FOCAL_LENGTH = 551.8902 (vs 默认800)

### 4. **稳定可靠**
- 参考立创官方示例
- 完整的错误处理
- 按键消抖处理

## 🔧 故障排除

### 问题1：按键无响应
**解决方案**：
- 检查GPIO53引脚连接
- 确认按键硬件正常
- 系统会自动降级为延时启动

### 问题2：按键误触发
**解决方案**：
- 已实现200ms消抖延时
- 使用上升沿检测避免重复触发

### 问题3：初始化失败
**解决方案**：
- 系统自动检测初始化状态
- 失败时使用3秒延时替代
- 不影响校准功能正常使用

## 📝 总结

通过将"按任意键开始"改为GPIO53按键控制，校准工具现在完全适配K230嵌入式环境，提供了更好的用户体验和更高的可靠性。同时同步了用户已校准的精确参数，确保测量精度。
