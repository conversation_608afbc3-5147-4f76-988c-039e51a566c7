# K230 Rectangle Recognition - Ultra Minimal Memory Version
# Copyright: Mi Cu Electronics Studio
# Developer: <PERSON> (Mike <PERSON> Engineer)
# Optimized for K230 memory constraints

import time, os, sys
from media.sensor import *
from media.display import *
from media.media import *

# Ultra-minimal configuration
WIDTH = 160
HEIGHT = 120
SENSOR_ID = 2

# Global variables to minimize memory allocation
outer_rect = None
inner_rect = None
shape_name = "None"
distances = [0, 0, 0, 0]  # left, right, top, bottom

def find_rects(img):
    """Find rectangles with absolute minimal memory usage"""
    global outer_rect, inner_rect
    
    try:
        # Direct processing on original image to save memory
        img.binary([(0, 85)])  # Convert to binary for black detection
        
        # Find rectangles with very low threshold
        rects = img.find_rects(threshold=600)
        
        rect_count = len(rects)
        if rect_count >= 2:
            # Sort by area - largest first
            rects.sort(key=lambda r: r.w() * r.h(), reverse=True)
            outer_rect = rects[0]
            inner_rect = rects[1]
            print("Found 2 rects")
            return 2
        elif rect_count == 1:
            outer_rect = rects[0]
            inner_rect = None
            print("Found 1 rect")
            return 1
        else:
            outer_rect = None
            inner_rect = None
            print("No rects")
            return 0
            
    except Exception as e:
        print("Rect err")
        return -1

def calc_distances():
    """Calculate distances with minimal operations"""
    global outer_rect, inner_rect, distances
    
    if not outer_rect or not inner_rect:
        distances = [0, 0, 0, 0]
        return
        
    try:
        ox, oy, ow, oh = outer_rect.rect()
        ix, iy, iw, ih = inner_rect.rect()
        
        distances[0] = abs(ix - ox)  # left
        distances[1] = abs((ox + ow) - (ix + iw))  # right
        distances[2] = abs(iy - oy)  # top
        distances[3] = abs((oy + oh) - (iy + ih))  # bottom
        
        print("Dist:", distances[0], distances[1], distances[2], distances[3])
        
    except Exception as e:
        print("Dist err")

def detect_shape(img):
    """Minimal shape detection"""
    global inner_rect, shape_name
    
    if not inner_rect:
        shape_name = "None"
        return
        
    try:
        ix, iy, iw, ih = inner_rect.rect()
        
        # Skip if too small
        if iw < 12 or ih < 12:
            shape_name = "Small"
            return
            
        # Minimal ROI
        roi = (ix + 2, iy + 2, iw - 4, ih - 4)
        
        # Try circle detection only (most memory efficient)
        circles = img.find_circles(roi=roi, threshold=1000, x_stride=4, y_stride=4)
        if len(circles) > 0:
            shape_name = "Circle"
            print("Shape: Circle")
            return
            
        # Try basic line detection
        lines = img.find_line_segments(roi=roi, merge_distance=20)
        line_count = len(lines)
        
        if line_count == 3:
            shape_name = "Triangle"
        elif line_count == 4:
            shape_name = "Rect"
        elif line_count > 4:
            shape_name = "Multi"
        else:
            shape_name = "Unknown"
            
        print("Shape:", shape_name, "Lines:", line_count)
        
    except Exception as e:
        shape_name = "Error"
        print("Shape err")

def draw_simple(img):
    """Ultra-minimal drawing"""
    global outer_rect, inner_rect, shape_name, distances
    
    try:
        # Draw outer rectangle (red)
        if outer_rect:
            img.draw_rectangle(outer_rect.rect(), color=(255, 0, 0), thickness=1)
            
        # Draw inner rectangle (green)
        if inner_rect:
            img.draw_rectangle(inner_rect.rect(), color=(0, 255, 0), thickness=1)
            
            # Draw shape name in center
            cx = inner_rect.x() + inner_rect.w() // 2
            cy = inner_rect.y() + inner_rect.h() // 2
            img.draw_string_advanced(cx - 15, cy, 12, shape_name, color=(0, 0, 255))
            
            # Draw minimal distance info
            if distances[0] > 2:  # left distance
                img.draw_string_advanced(outer_rect.x() + 1, 
                                       outer_rect.y() + outer_rect.h() // 2, 
                                       8, str(distances[0]), color=(255, 255, 0))
        
        # Draw minimal status
        status = ("O" if outer_rect else "X") + ("I" if inner_rect else "X")
        img.draw_string_advanced(2, 2, 8, status, color=(255, 255, 255))
        
        # Draw shape type
        img.draw_string_advanced(2, 12, 8, shape_name, color=(255, 255, 255))
        
    except Exception as e:
        # Silent fail to save memory
        pass

def main():
    """Main function with absolute minimal memory usage"""
    sensor = None
    
    try:
        # Minimal sensor initialization
        sensor = Sensor(id=SENSOR_ID)
        sensor.reset()
        sensor.set_framesize(width=WIDTH, height=HEIGHT, chn=CAM_CHN_ID_0)
        sensor.set_pixformat(Sensor.RGB565, chn=CAM_CHN_ID_0)
        
        # Minimal display initialization
        Display.init(Display.ST7701, width=800, height=480, to_ide=True)
        
        # Start media
        MediaManager.init()
        sensor.run()
        
        print("K230 Ultra-Minimal Rectangle System")
        print("Memory optimized for stability")
        
        frame = 0
        while True:
            os.exitpoint()
            
            try:
                # Get image
                img = sensor.snapshot(chn=CAM_CHN_ID_0)
                
                # Process every 6th frame only to minimize memory usage
                if frame % 6 == 0:
                    rect_count = find_rects(img)
                    if rect_count >= 2:
                        calc_distances()
                        detect_shape(img)
                
                # Always draw (lightweight)
                draw_simple(img)
                
                # Display centered
                x_pos = (800 - WIDTH) // 2
                y_pos = (480 - HEIGHT) // 2
                Display.show_image(img, x=x_pos, y=y_pos)
                
                frame += 1
                time.sleep_ms(250)  # Very slow to prevent memory issues
                
            except Exception as e:
                print("Frame err")
                time.sleep_ms(500)
                continue
            
    except KeyboardInterrupt:
        print("User stop")
    except Exception as e:
        print("System err")
    finally:
        # Minimal cleanup
        try:
            if sensor:
                sensor.stop()
            Display.deinit()
            MediaManager.deinit()
        except:
            pass
        print("Cleanup done")

if __name__ == "__main__":
    main()
