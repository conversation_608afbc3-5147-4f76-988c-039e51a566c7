# K230 Rectangle Recognition - 基于LCD参考代码的正确实现
# Copyright: Mi Cu Electronics Studio
# Developer: <PERSON> (Mike Team Engineer)
# 完全基于您提供的LCD显示参考代码

import time
import os
import sys
import image
from media.sensor import *
from media.display import *
from media.media import *

# 显示模式选择 - 基于参考代码
DISPLAY_MODE = "LCD"

# 根据模式设置显示宽高 - 完全按照参考代码
if DISPLAY_MODE == "VIRT":
    # 虚拟显示器模式
    DISPLAY_WIDTH = ALIGN_UP(1920, 16)
    DISPLAY_HEIGHT = 1080
elif DISPLAY_MODE == "LCD":
    # 3.1寸屏幕模式
    DISPLAY_WIDTH = 800
    DISPLAY_HEIGHT = 480
elif DISPLAY_MODE == "HDMI":
    # HDMI扩展板模式
    DISPLAY_WIDTH = 1920
    DISPLAY_HEIGHT = 1080
else:
    raise ValueError("未知的 DISPLAY_MODE，请选择 'VIRT', 'LCD' 或 'HDMI'")

# 摄像头配置
PICTURE_WIDTH = 640
PICTURE_HEIGHT = 480

# 全局变量
sensor = None
outer_rect = None
inner_rect = None
shape_name = "None"
distances = [0, 0, 0, 0]  # left, right, top, bottom

def find_rectangles(img):
    """基于参考代码的矩形检测方法"""
    global outer_rect, inner_rect
    
    try:
        # 使用参考代码的方式 - 寻找色块来检测黑色矩形
        # 黑色阈值 - 检测黑色区域
        black_threshold = [(0, 30, -128, 127, -128, 127)]
        
        # 寻找黑色色块，参数参考示例代码
        blobs = img.find_blobs(black_threshold, False, 
                              (0, 0, img.width()//2, img.height()//2), 
                              x_stride=2, y_stride=2, 
                              pixels_threshold=1000, margin=True)
        
        if len(blobs) >= 2:
            # 按面积排序，最大的是外框，第二大的是内框
            blobs_sorted = sorted(blobs, key=lambda b: b.pixels(), reverse=True)
            outer_rect = blobs_sorted[0]
            inner_rect = blobs_sorted[1]
            print("Found outer and inner rectangles")
            return 2
        elif len(blobs) == 1:
            outer_rect = blobs[0]
            inner_rect = None
            print("Found single rectangle")
            return 1
        else:
            outer_rect = None
            inner_rect = None
            return 0
            
    except Exception as e:
        print("Rectangle detection error:", str(e))
        return -1

def calculate_distances():
    """计算内外边框距离"""
    global outer_rect, inner_rect, distances
    
    if not outer_rect or not inner_rect:
        distances = [0, 0, 0, 0]
        return
        
    try:
        # 使用blob对象的方法获取边界
        ox, oy, ow, oh = outer_rect.x(), outer_rect.y(), outer_rect.w(), outer_rect.h()
        ix, iy, iw, ih = inner_rect.x(), inner_rect.y(), inner_rect.w(), inner_rect.h()
        
        distances[0] = abs(ix - ox)  # left
        distances[1] = abs((ox + ow) - (ix + iw))  # right
        distances[2] = abs(iy - oy)  # top
        distances[3] = abs((oy + oh) - (iy + ih))  # bottom
        
        print("Distances - L:{} R:{} T:{} B:{}".format(distances[0], distances[1], distances[2], distances[3]))
        
    except Exception as e:
        print("Distance calculation error:", str(e))

def detect_inner_shape(img):
    """基于参考代码的形状检测"""
    global inner_rect, shape_name
    
    if not inner_rect:
        shape_name = "None"
        return
        
    try:
        ix, iy, iw, ih = inner_rect.x(), inner_rect.y(), inner_rect.w(), inner_rect.h()
        
        # Skip if too small
        if iw < 20 or ih < 20:
            shape_name = "Small"
            return
            
        # 设置ROI区域
        roi = (ix + 5, iy + 5, iw - 10, ih - 10)
        
        # 参考代码的线段检测方法
        img_line = img.to_rgb565(copy=True)
        img_line.midpoint_pool(2, 2)  # 降采样减少计算量
        
        # 在ROI区域内寻找线段
        lines = img_line.find_line_segments(roi, 10, 10)
        
        # 过滤短线段
        valid_lines = [line for line in lines if line.length() > 15]
        line_count = len(valid_lines)
        
        # 尝试圆形检测
        circles = img.find_circles(roi=roi, threshold=2000, x_stride=2, y_stride=2)
        
        if len(circles) > 0:
            shape_name = "Circle"
        elif line_count == 3:
            shape_name = "Triangle"
        elif line_count == 4:
            shape_name = "Rectangle"
        elif line_count > 4:
            shape_name = "Polygon"
        else:
            shape_name = "Unknown"
            
        print("Shape detected: {} (Lines: {}, Circles: {})".format(shape_name, line_count, len(circles)))
        
    except Exception as e:
        shape_name = "Error"
        print("Shape detection error:", str(e))

def draw_results_on_lcd_image(lcd_img, camera_img):
    """基于LCD参考代码的绘制方法"""
    global outer_rect, inner_rect, shape_name, distances
    
    try:
        # 清除LCD图像内容 - 基于参考代码
        lcd_img.clear()
        
        # 将摄像头图像绘制到LCD图像上 (居中显示)
        x_offset = (DISPLAY_WIDTH - PICTURE_WIDTH) // 2
        y_offset = 0
        
        # 复制摄像头图像
        lcd_img.draw_image(camera_img, x_offset, y_offset)
        
        # 在LCD图像上绘制检测结果
        if outer_rect:
            # 调整坐标到LCD显示区域
            ox = outer_rect.x() + x_offset
            oy = outer_rect.y() + y_offset
            ow = outer_rect.w()
            oh = outer_rect.h()
            
            # 绘制外边框 (红色) - LCD大字体
            lcd_img.draw_rectangle(ox, oy, ow, oh, color=(255, 0, 0), thickness=5, fill=False)

        if inner_rect:
            # 调整坐标到LCD显示区域
            ix = inner_rect.x() + x_offset
            iy = inner_rect.y() + y_offset
            iw = inner_rect.w()
            ih = inner_rect.h()
            
            # 绘制内边框 (绿色)
            lcd_img.draw_rectangle(ix, iy, iw, ih, color=(0, 255, 0), thickness=4, fill=False)

            # 在内边框中心绘制形状名称 - LCD大字体
            cx = ix + iw // 2
            cy = iy + ih // 2
            lcd_img.draw_string_advanced(cx - 50, cy, 40, shape_name, color=(0, 0, 255))

            # 绘制距离信息 - LCD大字体
            if distances[0] > 0:
                lcd_img.draw_string_advanced(ox + 15, oy + 50, 28, "L:{}".format(distances[0]), color=(255, 255, 0))
                lcd_img.draw_string_advanced(ox + ow - 100, oy + 50, 28, "R:{}".format(distances[1]), color=(255, 255, 0))
                lcd_img.draw_string_advanced(ox + ow // 2 - 40, oy + 15, 28, "T:{}".format(distances[2]), color=(255, 255, 0))
                lcd_img.draw_string_advanced(ox + ow // 2 - 40, oy + oh - 40, 28, "B:{}".format(distances[3]), color=(255, 255, 0))

        # LCD屏幕状态信息 - 参考代码风格的大字体
        status = "Outer:{} Inner:{} Shape:{}".format(
            "YES" if outer_rect else "NO",
            "YES" if inner_rect else "NO",
            shape_name
        )
        lcd_img.draw_string_advanced(30, DISPLAY_HEIGHT - 80, 32, status, color=(255, 255, 255))
        
        # LCD屏幕标题 - 参考代码风格
        lcd_img.draw_string_advanced(30, 30, 48, "K230 LCD Rectangle System", color=(0, 255, 255))

    except Exception as e:
        print("LCD drawing error:", str(e))

# 主程序 - 完全基于LCD参考代码
def rectangle_recognition_lcd():
    global sensor
    
    print(f"K230 Rectangle Recognition - LCD Display Mode")

    # 创建用于LCD绘图的图像对象 - 基于参考代码
    lcd_img = image.Image(DISPLAY_WIDTH, DISPLAY_HEIGHT, image.ARGB8888)

    # 根据模式初始化显示器 - 完全按照参考代码
    if DISPLAY_MODE == "VIRT":
        Display.init(Display.VIRT, width=DISPLAY_WIDTH, height=DISPLAY_HEIGHT, fps=60)
    elif DISPLAY_MODE == "LCD":
        Display.init(Display.ST7701, width=DISPLAY_WIDTH, height=DISPLAY_HEIGHT, to_ide=True)
    elif DISPLAY_MODE == "HDMI":
        Display.init(Display.LT9611, width=DISPLAY_WIDTH, height=DISPLAY_HEIGHT, to_ide=True)

    # 初始化媒体管理器 - 基于参考代码
    MediaManager.init()
    
    # 初始化摄像头
    sensor = Sensor(width=PICTURE_WIDTH, height=PICTURE_HEIGHT)
    sensor.reset()
    sensor.set_framesize(width=PICTURE_WIDTH, height=PICTURE_HEIGHT)
    sensor.set_pixformat(Sensor.RGB565)
    sensor.run()

    print("K230 LCD display system ready!")

    try:
        while True:
            # 获取摄像头图像
            camera_img = sensor.snapshot(chn=CAM_CHN_ID_0)

            # 执行矩形检测
            rect_count = find_rectangles(camera_img)
            if rect_count >= 2:
                calculate_distances()
                detect_inner_shape(camera_img)

            # 在LCD图像上绘制结果 - 基于参考代码方法
            draw_results_on_lcd_image(lcd_img, camera_img)

            # 将绘制结果显示到LCD屏幕 - 基于参考代码
            Display.show_image(lcd_img)

            time.sleep_ms(100)  # 控制刷新率
            os.exitpoint()  # 可用的退出点 - 基于参考代码
            
    except KeyboardInterrupt as e:
        print("用户终止：", e)  # 捕获键盘中断异常
    except BaseException as e:
        print(f"异常：{e}")  # 捕获其他异常
    finally:
        # 清理资源 - 基于参考代码
        if isinstance(sensor, Sensor):
            sensor.stop()
        Display.deinit()
        os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)  # 启用睡眠模式的退出点
        time.sleep_ms(100)  # 延迟100毫秒
        MediaManager.deinit()
        print("K230 LCD display system stopped")

# 主程序入口 - 基于参考代码
if __name__ == "__main__":
    os.exitpoint(os.EXITPOINT_ENABLE)  # 启用退出点
    rectangle_recognition_lcd()  # 调用矩形识别LCD显示函数
