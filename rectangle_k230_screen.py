# K230 Rectangle Recognition - K230屏幕显示专用版本
# Copyright: Mi Cu Electronics Studio
# Developer: <PERSON> (Mike Team Engineer)
# 专门为K230本地屏幕显示优化

import time
import os
import sys

from media.sensor import *
from media.display import *
from media.media import *

# 全局变量
sensor = None
outer_rect = None
inner_rect = None
shape_name = "None"
distances = [0, 0, 0, 0]  # left, right, top, bottom

def find_rectangles(img):
    """基于参考代码的矩形检测方法"""
    global outer_rect, inner_rect
    
    try:
        # 使用参考代码的方式 - 寻找色块来检测黑色矩形
        # 黑色阈值 - 检测黑色区域
        black_threshold = [(0, 30, -128, 127, -128, 127)]
        
        # 寻找黑色色块，参数参考示例代码
        blobs = img.find_blobs(black_threshold, False, 
                              (0, 0, img.width()//2, img.height()//2), 
                              x_stride=2, y_stride=2, 
                              pixels_threshold=1000, margin=True)
        
        if len(blobs) >= 2:
            # 按面积排序，最大的是外框，第二大的是内框
            blobs_sorted = sorted(blobs, key=lambda b: b.pixels(), reverse=True)
            outer_rect = blobs_sorted[0]
            inner_rect = blobs_sorted[1]
            print("Found outer and inner rectangles")
            return 2
        elif len(blobs) == 1:
            outer_rect = blobs[0]
            inner_rect = None
            print("Found single rectangle")
            return 1
        else:
            outer_rect = None
            inner_rect = None
            return 0
            
    except Exception as e:
        print("Rectangle detection error:", str(e))
        return -1

def calculate_distances():
    """计算内外边框距离"""
    global outer_rect, inner_rect, distances
    
    if not outer_rect or not inner_rect:
        distances = [0, 0, 0, 0]
        return
        
    try:
        # 使用blob对象的方法获取边界
        ox, oy, ow, oh = outer_rect.x(), outer_rect.y(), outer_rect.w(), outer_rect.h()
        ix, iy, iw, ih = inner_rect.x(), inner_rect.y(), inner_rect.w(), inner_rect.h()
        
        distances[0] = abs(ix - ox)  # left
        distances[1] = abs((ox + ow) - (ix + iw))  # right
        distances[2] = abs(iy - oy)  # top
        distances[3] = abs((oy + oh) - (iy + ih))  # bottom
        
        print("Distances - L:{} R:{} T:{} B:{}".format(distances[0], distances[1], distances[2], distances[3]))
        
    except Exception as e:
        print("Distance calculation error:", str(e))

def detect_inner_shape(img):
    """基于参考代码的形状检测"""
    global inner_rect, shape_name
    
    if not inner_rect:
        shape_name = "None"
        return
        
    try:
        ix, iy, iw, ih = inner_rect.x(), inner_rect.y(), inner_rect.w(), inner_rect.h()
        
        # Skip if too small
        if iw < 20 or ih < 20:
            shape_name = "Small"
            return
            
        # 设置ROI区域
        roi = (ix + 5, iy + 5, iw - 10, ih - 10)
        
        # 参考代码的线段检测方法
        img_line = img.to_rgb565(copy=True)
        img_line.midpoint_pool(2, 2)  # 降采样减少计算量
        
        # 在ROI区域内寻找线段
        lines = img_line.find_line_segments(roi, 10, 10)
        
        # 过滤短线段
        valid_lines = [line for line in lines if line.length() > 15]
        line_count = len(valid_lines)
        
        # 尝试圆形检测
        circles = img.find_circles(roi=roi, threshold=2000, x_stride=2, y_stride=2)
        
        if len(circles) > 0:
            shape_name = "Circle"
        elif line_count == 3:
            shape_name = "Triangle"
        elif line_count == 4:
            shape_name = "Rectangle"
        elif line_count > 4:
            shape_name = "Polygon"
        else:
            shape_name = "Unknown"
            
        print("Shape detected: {} (Lines: {}, Circles: {})".format(shape_name, line_count, len(circles)))
        
    except Exception as e:
        shape_name = "Error"
        print("Shape detection error:", str(e))

def draw_results_for_k230_screen(img):
    """专门为K230屏幕优化的绘制方法"""
    global outer_rect, inner_rect, shape_name, distances
    
    try:
        # 绘制外边框 (红色) - 加粗线条便于K230屏幕观看
        if outer_rect:
            img.draw_rectangle(outer_rect.x(), outer_rect.y(), outer_rect.w(), outer_rect.h(), 
                             color=(255, 0, 0), thickness=5, fill=False)
            
        # 绘制内边框 (绿色) - 加粗线条
        if inner_rect:
            img.draw_rectangle(inner_rect.x(), inner_rect.y(), inner_rect.w(), inner_rect.h(), 
                             color=(0, 255, 0), thickness=4, fill=False)
            
            # 在内边框中心绘制形状名称 - 大字体便于K230屏幕观看
            cx = inner_rect.x() + inner_rect.w() // 2
            cy = inner_rect.y() + inner_rect.h() // 2
            img.draw_string_advanced(cx - 40, cy, 30, shape_name, color=(0, 0, 255))
            
            # 绘制距离信息 - 大字体和明显颜色
            if distances[0] > 0:
                # 左边距离
                img.draw_string_advanced(outer_rect.x() + 10, outer_rect.y() + 40, 
                                       24, "L:{}".format(distances[0]), color=(255, 255, 0))
                # 右边距离
                img.draw_string_advanced(outer_rect.x() + outer_rect.w() - 80, outer_rect.y() + 40, 
                                       24, "R:{}".format(distances[1]), color=(255, 255, 0))
                # 上边距离
                img.draw_string_advanced(outer_rect.x() + outer_rect.w() // 2 - 30, outer_rect.y() + 10, 
                                       24, "T:{}".format(distances[2]), color=(255, 255, 0))
                # 下边距离
                img.draw_string_advanced(outer_rect.x() + outer_rect.w() // 2 - 30, 
                                       outer_rect.y() + outer_rect.h() - 30, 
                                       24, "B:{}".format(distances[3]), color=(255, 255, 0))
        
        # K230屏幕状态信息显示区域 - 大字体和高对比度
        # 背景框
        img.draw_rectangle(10, img.height() - 120, 600, 110, color=(0, 0, 0), thickness=2, fill=True)
        img.draw_rectangle(10, img.height() - 120, 600, 110, color=(255, 255, 255), thickness=3, fill=False)
        
        # 状态文字 - 白色大字体
        status_line1 = "Outer:{} Inner:{}".format(
            "YES" if outer_rect else "NO",
            "YES" if inner_rect else "NO"
        )
        status_line2 = "Shape: {}".format(shape_name)
        
        img.draw_string_advanced(20, img.height() - 110, 24, status_line1, color=(255, 255, 255))
        img.draw_string_advanced(20, img.height() - 80, 24, status_line2, color=(255, 255, 255))
        img.draw_string_advanced(20, img.height() - 50, 20, "K230 Screen Display", color=(0, 255, 255))
        
    except Exception as e:
        print("Drawing error:", str(e))

# 主程序 - K230屏幕显示专用
try:
    print("=== K230 Rectangle Recognition - Screen Display ===")

    # 参考代码的初始化方式
    sensor = Sensor(width=640, height=480)
    sensor.reset()

    sensor.set_framesize(width=640, height=480)
    sensor.set_pixformat(Sensor.RGB565)

    # 初始化显示器 - 专门为K230屏幕优化
    # LT9611支持HDMI输出，可以连接到K230的屏幕
    Display.init(Display.LT9611, to_ide=True)
    print("Display initialized - K230 screen ready")
    
    # 初始化媒体管理器
    MediaManager.init()
    # 启动 sensor
    sensor.run()
    clock = time.clock()

    print("System ready - Check your K230 screen!")

    while True:
        clock.tick()
        os.exitpoint()
        img = sensor.snapshot(chn=CAM_CHN_ID_0)

        # 执行矩形检测
        rect_count = find_rectangles(img)
        if rect_count >= 2:
            calculate_distances()
            detect_inner_shape(img)

        # 使用K230屏幕优化的绘制方法
        draw_results_for_k230_screen(img)

        # 显示FPS和系统信息 - K230屏幕专用大字体
        img.draw_string_advanced(10, 10, 36, "FPS: {:.1f}".format(clock.fps()), color=(255, 0, 0))
        img.draw_string_advanced(10, 60, 24, "K230 Rectangle System", color=(0, 255, 255))
        
        # 压缩图像用于IDE传输（可选）
        img.compressed_for_ide()
        
        # 显示到K230屏幕和IDE
        Display.show_image(img)

except KeyboardInterrupt as e:
    print("用户停止: ", e)
except BaseException as e:
    print("异常: {}".format(e))
finally:
    if isinstance(sensor, Sensor):
        sensor.stop()
    Display.deinit()
    os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
    time.sleep_ms(100)
    MediaManager.deinit()
    print("K230 screen display system stopped")
