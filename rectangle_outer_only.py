# K230矩形识别系统 - 只检测外框版本
# 功能：只检测外框矩形 + 形状识别 + LCD显示
# 作者：米醋电子工作室技术团队
# 版本：V12.0 Outer Rectangle Only

import time
import os
import sys
import gc

from media.sensor import *
from media.display import *
from media.media import *

# 显示配置参数 - 完全基于您的工作参考代码
DETECT_WIDTH = ALIGN_UP(640, 16)
DETECT_HEIGHT = 480

# 矩形检测参数 - 基于您的工作参考代码
LAB_PARAMS = {
    # 黑色LAB阈值 - 基于您的工作参考代码
    'black_threshold': [(0, 30, -128, 127, -128, 127)],
    
    # 矩形参数
    'rect_min_area': 1000,
    'rect_max_area': 300000
}

class OuterRectangleDetector:
    def __init__(self):
        """初始化外框矩形检测器"""
        self.sensor = None
        self.detected_blobs = []
        self.outer_rect = None
        self.shape_name = "None"

    def init_system(self):
        """初始化系统 - 完全基于您的工作参考代码"""
        print("🎯 Initializing Outer Rectangle Detection System...")
        print(f"🔬 LAB Threshold: {LAB_PARAMS['black_threshold'][0]}")

        # 启用退出点 - 基于您的工作参考代码
        os.exitpoint(os.EXITPOINT_ENABLE)

        # 初始化摄像头 - 完全基于您的工作参考代码
        self.sensor = Sensor(width=DETECT_WIDTH, height=DETECT_HEIGHT)
        self.sensor.reset()
        self.sensor.set_framesize(width=DETECT_WIDTH, height=DETECT_HEIGHT)
        self.sensor.set_pixformat(Sensor.RGB565)
        print(f"✅ Camera configured: {DETECT_WIDTH}x{DETECT_HEIGHT}")

        # 初始化显示 - 完全基于您的工作参考代码
        Display.init(Display.ST7701, to_ide=True)
        print("✅ LCD Display initialized (ST7701)")

        # 初始化媒体管理器 - 基于您的工作参考代码
        MediaManager.init()
        print("✅ Media manager initialized")
        
        # 启动摄像头 - 基于您的工作参考代码
        self.sensor.run()
        print("✅ Camera started")

        print("✅ Outer Rectangle Detection System initialized!")

    def find_outer_rectangle(self, img):
        """只检测外框矩形 - 简化版本"""
        try:
            # 使用您的工作参考代码的LAB阈值检测黑色区域
            black_blobs = img.find_blobs(LAB_PARAMS['black_threshold'],
                                        pixels_threshold=50,
                                        area_threshold=50,
                                        merge=True)

            self.detected_blobs = black_blobs
            
            if len(black_blobs) >= 1:
                # 选择最大的矩形作为外框
                largest_blob = max(black_blobs, key=lambda b: b.pixels())
                
                # 检查面积是否符合要求
                area = largest_blob.w() * largest_blob.h()
                if LAB_PARAMS['rect_min_area'] <= area <= LAB_PARAMS['rect_max_area']:
                    self.outer_rect = largest_blob
                    return 1
                else:
                    self.outer_rect = None
                    return 0
            else:
                self.outer_rect = None
                return 0
                
        except Exception as e:
            print("Rectangle detection error:", str(e))
            return -1

    def detect_shape_in_rectangle(self, img):
        """检测矩形内部的形状 - 简化版本"""
        if not self.outer_rect:
            self.shape_name = "None"
            return
            
        try:
            ox, oy, ow, oh = self.outer_rect.x(), self.outer_rect.y(), self.outer_rect.w(), self.outer_rect.h()
            
            # Skip if too small
            if ow < 40 or oh < 40:
                self.shape_name = "Small"
                return
                
            # 设置ROI区域 - 在矩形内部
            roi = (ox + 10, oy + 10, ow - 20, oh - 20)
            
            # 您的工作参考代码的线段检测方法
            img_line = img.to_rgb565(copy=True)
            img_line.midpoint_pool(2, 2)  # 降采样减少计算量
            
            # 在ROI区域内寻找线段
            lines = img_line.find_line_segments(roi, 10, 10)
            
            # 过滤短线段
            valid_lines = [line for line in lines if line.length() > 20]
            line_count = len(valid_lines)
            
            # 尝试圆形检测
            circles = img.find_circles(roi=roi, threshold=2000, x_stride=2, y_stride=2)
            
            if len(circles) > 0:
                self.shape_name = "Circle"
            elif line_count == 3:
                self.shape_name = "Triangle"
            elif line_count == 4:
                self.shape_name = "Rectangle"
            elif line_count > 4:
                self.shape_name = "Polygon"
            else:
                self.shape_name = "Unknown"
                
        except Exception as e:
            self.shape_name = "Error"
            print("Shape detection error:", str(e))

    def draw_results(self, img):
        """绘制检测结果 - 只显示外框"""
        try:
            # 绘制所有检测到的色块 (蓝色边框) - 基于您的工作参考代码
            for blob in self.detected_blobs:
                img.draw_rectangle(blob.x(), blob.y(), blob.w(), blob.h(),
                                 color=(0, 0, 255), thickness=1, fill=False)

            # 绘制画面中心十字线 (黄色) - 基于您的工作参考代码
            center_x = DETECT_WIDTH // 2
            center_y = DETECT_HEIGHT // 2
            img.draw_line(center_x - 20, center_y, center_x + 20, center_y, color=(255, 255, 0), thickness=2)
            img.draw_line(center_x, center_y - 20, center_x, center_y + 20, color=(255, 255, 0), thickness=2)

            # 绘制外框矩形 (红色粗线) - 基于您的工作参考代码风格
            if self.outer_rect:
                ox, oy, ow, oh = self.outer_rect.x(), self.outer_rect.y(), self.outer_rect.w(), self.outer_rect.h()
                
                # 绘制外框
                img.draw_rectangle(ox, oy, ow, oh, color=(255, 0, 0), thickness=4, fill=False)

                # 在矩形中心绘制形状名称
                cx = ox + ow // 2
                cy = oy + oh // 2
                img.draw_string_advanced(cx - 40, cy, 32, self.shape_name, color=(0, 255, 0))

                # 显示矩形信息
                info_text = "{}x{} Area:{}".format(ow, oh, ow * oh)
                img.draw_string_advanced(ox + 10, oy + 30, 16, info_text, color=(255, 255, 0))

                # 显示矩形中心坐标
                center_text = "Center:({},{})".format(cx, cy)
                img.draw_string_advanced(ox + 10, oy + 50, 16, center_text, color=(255, 255, 0))

            # 状态信息 - 简化版本
            status = "Rectangle:{} Shape:{}".format(
                "YES" if self.outer_rect else "NO",
                self.shape_name
            )
            img.draw_string_advanced(10, DETECT_HEIGHT - 40, 16, status, color=(255, 255, 255))

        except Exception as e:
            print("Drawing error:", str(e))

    def run(self):
        """运行检测主循环 - 基于您的工作参考代码"""
        print("🎯 Starting Outer Rectangle Detection...")
        print(f"🔬 LAB Threshold: {LAB_PARAMS['black_threshold'][0]}")
        print("⌨️ Press Ctrl+C to stop")

        fps_counter = time.clock()
        frame_count = 0

        try:
            while True:
                fps_counter.tick()
                os.exitpoint()
                frame_count += 1

                try:
                    # 获取图像 - 基于您的工作参考代码
                    img = self.sensor.snapshot()

                    # 执行外框矩形检测
                    rect_count = self.find_outer_rectangle(img)
                    if rect_count >= 1:
                        self.detect_shape_in_rectangle(img)

                    # 绘制结果
                    self.draw_results(img)

                    # 显示FPS和状态 - 基于您的工作参考代码风格
                    status = "Found" if self.outer_rect else "None"
                    fps_text = f"FPS: {fps_counter.fps():.1f} | {status}"
                    img.draw_string_advanced(DETECT_WIDTH-200, 20, 16, fps_text, color=(255, 255, 0))

                    # 显示标题
                    img.draw_string_advanced(10, 20, 16, "K230 Outer Rectangle Only", color=(0, 255, 255))

                    # 显示图像 - 基于您的工作参考代码
                    Display.show_image(img)
                    gc.collect()

                    # 每100帧打印一次状态
                    if frame_count % 100 == 0:
                        print(f"📊 Frame {frame_count}: FPS={fps_counter.fps():.1f}, Status={status}")
                        if self.outer_rect:
                            ox, oy, ow, oh = self.outer_rect.x(), self.outer_rect.y(), self.outer_rect.w(), self.outer_rect.h()
                            print(f"   Rectangle: {ow}x{oh} at ({ox},{oy}), Shape: {self.shape_name}")

                except Exception as e:
                    print(f"⚠️ Frame processing error: {e}")
                    time.sleep_ms(100)
                    continue

        except KeyboardInterrupt:
            print("\n🛑 Detection stopped")
        except Exception as e:
            print(f"❌ Error: {e}")

    def cleanup(self):
        """清理资源 - 基于您的工作参考代码"""
        print("🧹 Cleaning up...")
        if self.sensor:
            self.sensor.stop()
        Display.deinit()
        os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
        time.sleep_ms(100)
        MediaManager.deinit()
        print("✅ Detector stopped.")

def main():
    """主函数 - 基于您的工作参考代码"""
    print("🎯 K230 Outer Rectangle Detection System")
    print("📅 Version 12.0 - Outer Rectangle Only")
    print("🏭 Powered by 米醋电子工作室")
    print(f"🔬 LAB Threshold: {LAB_PARAMS['black_threshold'][0]}")
    print("🎯 Function: Detect outer rectangle + shape recognition")

    detector = OuterRectangleDetector()

    try:
        detector.init_system()
        detector.run()
    except Exception as e:
        print(f"💥 System error: {e}")
        import sys
        sys.print_exception(e)
    finally:
        detector.cleanup()

if __name__ == "__main__":
    main()
