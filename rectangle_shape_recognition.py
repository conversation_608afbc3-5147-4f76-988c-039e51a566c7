# K230 Distance Measurement System - 基于A4纸标记的距离测量
# Copyright: Mi Cu Electronics Studio
# Developer: <PERSON> (Mike Team Engineer)
# 将OpenCV代码完全转换为K230兼容版本

import time
import os
import sys
import math
import image

# K230专用导入
try:
    from media.sensor import Sensor
    from media.display import Display
    from media.media import MediaManager
    print("✓ K230 media modules imported successfully")
except ImportError as e:
    print(f"❌ K230 import error: {e}")
    # 备用导入（用于开发环境测试）
    class Sensor:
        RGB565 = "RGB565"
        def __init__(self, width=1024, height=768): pass
        def reset(self): pass
        def set_framesize(self, width=1024, height=768): pass
        def set_pixformat(self, fmt): pass
        def run(self): pass
        def snapshot(self): return None
        def stop(self): pass

    class Display:
        ST7701 = "ST7701"
        @staticmethod
        def init(driver, to_ide=True): pass
        @staticmethod
        def show_image(img, x=0, y=0): pass
        @staticmethod
        def deinit(): pass

    class MediaManager:
        @staticmethod
        def init(): pass
        @staticmethod
        def deinit(): pass

# K230距离测量系统配置
DISPLAY_MODE = "LCD"  # K230 3.1寸屏幕模式
DISPLAY_WIDTH = 800
DISPLAY_HEIGHT = 480

# 摄像头配置
PICTURE_WIDTH = 640
PICTURE_HEIGHT = 480

# 距离测量参数 - 基于A4纸标记
KNOWN_WIDTH = 10.31  # A4纸的实际宽度 (inch) - 约28cm
FOCAL_LENGTH = 501.7184  # 焦距 (pixels) - 修正偏差后的值 (原551.8902 / 1.1)

# 全局变量
sensor = None
current_distance = 0.0
marker_found = False

def find_marker(img):
    """K230版本：检测A4纸标记并计算距离"""
    global marker_found, current_distance

    try:
        print("=== A4纸标记检测开始 ===")

        # 转换为灰度图像进行边缘检测
        # K230的image库没有直接的灰度转换，使用LAB颜色空间的L通道

        # 使用边缘检测方法：检测黑色边框
        # 在K230中，我们使用find_blobs来模拟轮廓检测
        black_threshold = [(0, 50, -128, 127, -128, 127)]  # LAB颜色空间的黑色

        # 设置搜索区域，避免边缘噪声
        margin = 30
        search_roi = (margin, margin, PICTURE_WIDTH - 2*margin, PICTURE_HEIGHT - 2*margin)

        # 检测黑色区域（模拟轮廓检测）
        blobs = img.find_blobs(black_threshold, False, search_roi,
                              x_stride=2, y_stride=2,
                              pixels_threshold=500, margin=True)

        if len(blobs) == 0:
            marker_found = False
            print("❌ 未检测到A4纸标记")
            return None

        print(f"检测到 {len(blobs)} 个候选标记")

        # 筛选最可能的A4纸标记
        valid_markers = []
        for blob in blobs:
            bx, by, bw, bh = blob.x(), blob.y(), blob.w(), blob.h()
            area = blob.pixels()
            bbox_area = bw * bh
            fill_ratio = area / bbox_area if bbox_area > 0 else 0
            aspect_ratio = max(bw, bh) / min(bw, bh) if min(bw, bh) > 0 else 1.0

            # A4纸标记的特征判断
            is_a4_marker = (
                # 1. 尺寸合理：不能太小也不能太大
                50 < bw < PICTURE_WIDTH * 0.8 and
                50 < bh < PICTURE_HEIGHT * 0.8 and
                # 2. 面积合理
                2000 < area < PICTURE_WIDTH * PICTURE_HEIGHT * 0.6 and
                # 3. 填充比例：A4纸边框应该是空心的
                0.1 < fill_ratio < 0.6 and
                # 4. 长宽比：A4纸的长宽比约为1.414 (√2)
                1.2 < aspect_ratio < 1.8
            )

            if is_a4_marker:
                valid_markers.append((blob, aspect_ratio, area))
                print(f"  ✓ 有效A4标记: {bw}x{bh}, 长宽比={aspect_ratio:.2f}, 面积={area}")

        if len(valid_markers) == 0:
            marker_found = False
            print("❌ 未找到符合A4纸特征的标记")
            return None

        # 选择最佳标记（最接近A4纸长宽比的）
        best_marker = min(valid_markers, key=lambda x: abs(x[1] - 1.414))[0]

        # 创建标记矩形对象（模拟OpenCV的minAreaRect）
        class MarkerRect:
            def __init__(self, blob):
                self.blob = blob
                self._center = (blob.cx(), blob.cy())
                self._size = (blob.w(), blob.h())
                self._angle = 0  # K230的blob没有角度信息，假设为0

            def center(self):
                return self._center

            def size(self):
                return self._size

            def angle(self):
                return self._angle

        marker_found = True
        marker_rect = MarkerRect(best_marker)

        print(f"✓ 检测到A4纸标记: 中心=({marker_rect.center()[0]}, {marker_rect.center()[1]}), 尺寸={marker_rect.size()}")

        return marker_rect

    except Exception as e:
        print(f"标记检测错误: {e}")
        marker_found = False
        return None


def distance_to_camera(knownWidth, focalLength, perWidth):
    """根据标记的像素宽度估计距离"""
    if perWidth <= 0:
        return 0
    return (knownWidth * focalLength) / perWidth

def calculate_marker_distance(marker_rect):
    """计算A4纸标记的距离"""
    global current_distance

    if not marker_rect:
        current_distance = 0.0
        return 0.0

    try:
        # 获取标记的像素宽度
        marker_width_pixels = marker_rect.size()[0]  # 使用宽度进行距离计算

        # 计算距离（英寸）
        distance_inches = distance_to_camera(KNOWN_WIDTH, FOCAL_LENGTH, marker_width_pixels)

        # 转换为厘米（1英寸 = 2.54厘米）
        distance_cm = distance_inches * 2.54

        current_distance = distance_cm

        print(f"距离计算: 像素宽度={marker_width_pixels}, 距离={distance_cm:.1f}cm ({distance_inches:.1f}inch)")

        return distance_cm

    except Exception as e:
        print(f"距离计算错误: {e}")
        current_distance = 0.0
        return 0.0

def draw_marker_and_distance(img, marker_rect, distance):
    """在图像上绘制标记和距离信息"""
    if not marker_rect:
        return

    try:
        # 获取标记信息
        center = marker_rect.center()
        size = marker_rect.size()

        # 计算矩形的四个角点（简化版本，假设没有旋转）
        half_w = size[0] // 2
        half_h = size[1] // 2

        # 绘制标记边框（绿色）
        rect_x = center[0] - half_w
        rect_y = center[1] - half_h
        img.draw_rectangle(rect_x, rect_y, size[0], size[1],
                          color=(0, 255, 0), thickness=3, fill=False)

        # 绘制中心点
        img.draw_circle(center[0], center[1], 5, color=(255, 0, 0), thickness=2, fill=True)

        # 显示距离信息
        if distance > 0:
            distance_text = f"Distance: {distance:.1f}cm"
            img.draw_string_advanced(PICTURE_WIDTH - 200, PICTURE_HEIGHT - 30, 20,
                                   distance_text, color=(0, 255, 0))

            # 显示英寸距离
            distance_inches = distance / 2.54
            inch_text = f"({distance_inches:.1f} inch)"
            img.draw_string_advanced(PICTURE_WIDTH - 200, PICTURE_HEIGHT - 10, 16,
                                   inch_text, color=(150, 150, 150))

        # 显示标记尺寸
        size_text = f"Marker: {size[0]}x{size[1]} px"
        img.draw_string_advanced(10, PICTURE_HEIGHT - 50, 16, size_text, color=(255, 255, 0))

        # 显示中心坐标
        center_text = f"Center: ({center[0]}, {center[1]})"
        img.draw_string_advanced(10, PICTURE_HEIGHT - 30, 16, center_text, color=(255, 255, 0))

    except Exception as e:
        print(f"绘制错误: {e}")

def find_rectangles_fallback(img):
    """备用边框检测方案：使用blob检测"""
    global outer_rect

    try:
        print("=== Fallback: Blob-based frame detection ===")

        # 使用blob检测作为备用方案
        black_threshold = [(0, 50, -128, 127, -128, 127)]
        margin = 20
        search_roi = (margin, margin, PICTURE_WIDTH - 2*margin, PICTURE_HEIGHT - 2*margin)

        blobs = img.find_blobs(black_threshold, False, search_roi,
                              x_stride=2, y_stride=2,
                              pixels_threshold=500, margin=True)

        if len(blobs) == 0:
            outer_rect = None
            print("No black regions found in fallback")
            return 0

        # 选择最合适的边框
        valid_frames = []
        for blob in blobs:
            bx, by, bw, bh = blob.x(), blob.y(), blob.w(), blob.h()
            area = blob.pixels()
            bbox_area = bw * bh
            fill_ratio = area / bbox_area if bbox_area > 0 else 0

            if (50 < bw < PICTURE_WIDTH * 0.8 and
                40 < bh < PICTURE_HEIGHT * 0.8 and
                0.1 < fill_ratio < 0.6):
                valid_frames.append((blob, fill_ratio))

        if len(valid_frames) == 0:
            outer_rect = None
            print("No valid frames in fallback")
            return 0

        # 选择最佳边框并扩展外圈
        best_blob = min(valid_frames, key=lambda x: abs(x[1] - 0.3))[0]
        bx, by, bw, bh = best_blob.x(), best_blob.y(), best_blob.w(), best_blob.h()

        # 扩展边框外圈（增加10像素边距）
        frame_x = max(0, bx - 10)
        frame_y = max(0, by - 10)
        frame_w = min(PICTURE_WIDTH - frame_x, bw + 20)
        frame_h = min(PICTURE_HEIGHT - frame_y, bh + 20)

        print(f"✓ Fallback expanded frame: {frame_w}x{frame_h} at ({frame_x},{frame_y})")

        class FrameRect:
            def __init__(self, x, y, w, h):
                self._x, self._y, self._w, self._h = x, y, w, h
            def x(self): return self._x
            def y(self): return self._y
            def w(self): return self._w
            def h(self): return self._h
            def pixels(self): return self._w * self._h
            def roundness(self): return 0.0

        outer_rect = FrameRect(frame_x, frame_y, frame_w, frame_h)
        return 1

    except Exception as e:
        print("Fallback frame detection error:", str(e))
        outer_rect = None
        return -1

def calculate_distances():
    """基于blob对象计算距离"""
    global outer_rect, inner_rect, distances

    if not outer_rect or not inner_rect:
        distances = [0, 0, 0, 0]
        return

    try:
        # 使用blob对象的方法获取边界
        ox, oy, ow, oh = outer_rect.x(), outer_rect.y(), outer_rect.w(), outer_rect.h()
        ix, iy, iw, ih = inner_rect.x(), inner_rect.y(), inner_rect.w(), inner_rect.h()

        distances[0] = abs(ix - ox)  # left
        distances[1] = abs((ox + ow) - (ix + iw))  # right
        distances[2] = abs(iy - oy)  # top
        distances[3] = abs((oy + oh) - (iy + ih))  # bottom

        print("Distances - L:{} R:{} T:{} B:{}".format(distances[0], distances[1], distances[2], distances[3]))

    except Exception as e:
        print("Distance calculation error:", str(e))

def detect_inner_shape(img):
    """严格检测边框内的实心黑色图形（三角形、矩形、圆形）"""
    global outer_rect, shape_name

    if not outer_rect:
        shape_name = "None"
        return

    try:
        print("=== Step 2: 实心内部图形检测 (严格过滤) ===")

        ox, oy, ow, oh = outer_rect.x(), outer_rect.y(), outer_rect.w(), outer_rect.h()

        # 设置内部搜索ROI区域，留足够边距避开边框
        inner_margin = max(min(ow, oh) // 5, 25)  # 更大边距确保完全避开边框
        roi = (ox + inner_margin, oy + inner_margin,
               ow - 2*inner_margin, oh - 2*inner_margin)

        # 确保ROI有效
        if roi[2] <= 30 or roi[3] <= 30:
            shape_name = "Small"
            print("ROI too small for inner shape detection")
            return

        print(f"Inner search ROI: {roi}, margin={inner_margin}")

        # 在ROI区域内检测黑色实心图形
        black_threshold = [(0, 45, -128, 127, -128, 127)]  # 中等严格的黑色检测

        inner_blobs = img.find_blobs(black_threshold, False, roi,
                                   x_stride=2, y_stride=2,
                                   pixels_threshold=200, margin=True)

        if len(inner_blobs) == 0:
            shape_name = "None"
            print("❌ No inner shapes found - trying more relaxed threshold...")

            # 尝试更宽松的阈值
            relaxed_threshold = [(0, 65, -128, 127, -128, 127)]
            inner_blobs = img.find_blobs(relaxed_threshold, False, roi,
                                       x_stride=3, y_stride=3,
                                       pixels_threshold=150, margin=True)

            if len(inner_blobs) == 0:
                print("❌ Still no inner shapes found")
                return

        print(f"📊 Found {len(inner_blobs)} potential inner shapes")

        # 严格过滤：只选择实心图形
        valid_inner_shapes = []
        for blob in inner_blobs:
            bx, by, bw, bh = blob.x(), blob.y(), blob.w(), blob.h()
            area = blob.pixels()
            bbox_area = bw * bh
            fill_ratio = area / bbox_area if bbox_area > 0 else 0
            roundness = blob.roundness()

            print(f"Inner shape analysis: pos=({bx},{by}), size={bw}x{bh}, fill={fill_ratio:.2f}, round={roundness:.2f}")

            # 实心图形的严格判断条件
            is_solid_shape = (
                # 1. 填充比例：实心图形应该填充比例很高
                fill_ratio > 0.7 and
                # 2. 尺寸合理：不能太大（避免误识别边框）也不能太小
                15 < bw < 120 and 15 < bh < 120 and
                # 3. 面积合理：避免噪点和大区域
                200 < area < 6000 and
                # 4. 在ROI中心区域（避免边框残留）
                bx > roi[0] + 10 and by > roi[1] + 10 and
                bx + bw < roi[0] + roi[2] - 10 and by + bh < roi[1] + roi[3] - 10
            )

            if is_solid_shape:
                valid_inner_shapes.append(blob)
                print(f"  ✓ 有效实心图形: fill={fill_ratio:.2f}, area={area}")
            else:
                print(f"  ❌ 不是有效实心图形")

        if len(valid_inner_shapes) == 0:
            shape_name = "None"
            print("❌ No valid solid inner shapes found")
            return

        print(f"✓ 找到 {len(valid_inner_shapes)} 个有效实心图形")

        # 选择最大的实心图形作为目标
        target_blob = max(valid_inner_shapes, key=lambda b: b.pixels())
        bx, by, bw, bh = target_blob.x(), target_blob.y(), target_blob.w(), target_blob.h()
        area = target_blob.pixels()
        bbox_area = bw * bh
        fill_ratio = area / bbox_area if bbox_area > 0 else 0
        roundness = target_blob.roundness()
        aspect_ratio = max(bw, bh) / min(bw, bh) if min(bw, bh) > 0 else 1.0

        print(f"Target solid shape: pos=({bx},{by}), size={bw}x{bh}, fill={fill_ratio:.2f}, round={roundness:.2f}, aspect={aspect_ratio:.2f}")

        # 优化的智能形状识别算法
        print(f"🔍 Shape analysis: round={roundness:.3f}, aspect={aspect_ratio:.3f}, fill={fill_ratio:.3f}")

        # 计算更多特征用于精确判断
        perimeter = target_blob.perimeter() if hasattr(target_blob, 'perimeter') else (2 * (bw + bh))
        compactness = (4 * math.pi * area) / (perimeter * perimeter) if perimeter > 0 else 0
        extent = area / bbox_area if bbox_area > 0 else 0

        print(f"🔍 Advanced features: compact={compactness:.3f}, extent={extent:.3f}, perimeter={perimeter}")

        # 多特征融合的智能形状判断
        circle_score = 0
        rectangle_score = 0
        triangle_score = 0

        # 圆形特征评分
        if roundness > 0.6:
            circle_score += 3
        elif roundness > 0.5:
            circle_score += 1

        if 0.8 < aspect_ratio < 1.25:  # 接近正方形
            circle_score += 2

        if compactness > 0.7:  # 圆形紧凑度高
            circle_score += 2

        if extent > 0.75:  # 圆形填充度高
            circle_score += 1

        # 矩形特征评分
        if roundness < 0.4:
            rectangle_score += 3
        elif roundness < 0.6:
            rectangle_score += 1

        if aspect_ratio > 1.2 or aspect_ratio < 0.8:  # 明显的长方形
            rectangle_score += 2
        elif 1.0 < aspect_ratio < 1.2:  # 接近正方形的矩形
            rectangle_score += 1

        if extent > 0.8:  # 矩形填充度很高
            rectangle_score += 2

        if 0.3 < compactness < 0.8:  # 矩形紧凑度中等
            rectangle_score += 1

        # 三角形特征评分
        if roundness < 0.5:
            triangle_score += 2

        if extent < 0.7:  # 三角形填充度相对较低
            triangle_score += 2

        if compactness < 0.6:  # 三角形紧凑度较低
            triangle_score += 2

        if aspect_ratio > 1.3:  # 三角形通常有明显的长宽比
            triangle_score += 1

        print(f"🎯 Shape scores: Circle={circle_score}, Rectangle={rectangle_score}, Triangle={triangle_score}")

        # 根据得分选择形状
        max_score = max(circle_score, rectangle_score, triangle_score)

        if max_score == circle_score and circle_score >= 4:
            # 圆形检测 - 使用参考代码的专业方法
            shape_name = "Circle"

            print(f"🔵 Circle candidate detected, using enhanced professional detection...")

            # 使用增强的圆形检测方法
            circle_result = enhanced_circle_detection(img, target_blob, roi)

            if circle_result:
                circle_x = circle_result['x']
                circle_y = circle_result['y']
                radius = circle_result['radius']
                diameter = circle_result['diameter']

                # 使用参考代码的绘制风格
                if circle_result['circle']:
                    # 专业检测成功 - 使用参考代码的绘制方法
                    img.draw_circle(circle_result['circle'].circle(), color=(1, 147, 230), thickness=3)
                    print(f"✓ Professional circle: center=({circle_x},{circle_y}), diameter={diameter}")
                else:
                    # 备用估算 - 使用标准绘制
                    img.draw_circle(circle_x, circle_y, radius, color=(0, 0, 255), thickness=3, fill=False)
                    print(f"✓ Estimated circle: center=({circle_x},{circle_y}), diameter={diameter}")

                # 绘制直径线 (绿色) - 统一风格
                img.draw_line(circle_x - radius, circle_y, circle_x + radius, circle_y,
                             color=(0, 255, 0), thickness=2)
                img.draw_line(circle_x, circle_y - radius, circle_x, circle_y + radius,
                             color=(0, 255, 0), thickness=2)

                # 显示直径 - 参考代码风格
                img.draw_string_advanced(circle_x - 35, circle_y - radius - 35, 22, f"Circle D:{diameter}", color=(255, 255, 0))

                # 显示检测方法
                method_text = "Professional" if circle_result['circle'] else "Estimated"
                img.draw_string_advanced(circle_x - 30, circle_y + radius + 10, 16, method_text, color=(150, 150, 150))

            else:
                print("❌ Circle detection completely failed")
                shape_name = "Circle_Error"

        elif max_score == rectangle_score and rectangle_score >= 3:
            # 矩形检测 - 优化版
            shape_name = "Rectangle"

            print(f"✓ Rectangle detected: {bw}x{bh}, score={rectangle_score}")

            # 绘制矩形轮廓 (绿色)
            img.draw_rectangle(bx, by, bw, bh, color=(0, 255, 0), thickness=3, fill=False)

            # 优化的标注位置
            mid_x = bx + bw // 2
            mid_y = by + bh // 2

            # 标注宽度
            img.draw_line(bx, by - 12, bx + bw, by - 12, color=(255, 255, 0), thickness=2)
            img.draw_string_advanced(mid_x - 20, by - 35, 20, f"W:{bw}", color=(255, 255, 0))

            # 标注高度
            img.draw_line(bx - 12, by, bx - 12, by + bh, color=(255, 255, 0), thickness=2)
            img.draw_string_advanced(bx - 50, mid_y - 10, 20, f"H:{bh}", color=(255, 255, 0))

            # 显示形状类型
            img.draw_string_advanced(mid_x - 30, mid_y - 10, 18, "Rectangle", color=(0, 255, 0))

        else:
            # 三角形检测 - 优化版
            shape_name = "Triangle"

            print(f"✓ Triangle detected: base={bw}, height={bh}, score={triangle_score}")

            # 绘制三角形边界框 (橙色)
            img.draw_rectangle(bx, by, bw, bh, color=(255, 165, 0), thickness=3, fill=False)

            # 估算三角形边长 - 更精确的计算
            base = bw
            height = bh
            # 假设等腰三角形，计算斜边
            side = int(math.sqrt(height**2 + (base/2)**2))

            # 优化的标注
            mid_x = bx + bw // 2

            # 标注底边
            img.draw_line(bx, by + bh + 12, bx + bw, by + bh + 12, color=(255, 255, 0), thickness=2)
            img.draw_string_advanced(mid_x - 30, by + bh + 18, 18, f"Base:{base}", color=(255, 255, 0))

            # 标注高度
            img.draw_line(bx - 12, by, bx - 12, by + bh, color=(255, 255, 0), thickness=2)
            img.draw_string_advanced(bx - 65, by + bh//2 - 9, 18, f"H:{height}", color=(255, 255, 0))

            # 标注斜边
            img.draw_string_advanced(bx + bw + 8, by + bh//2, 18, f"Side:{side}", color=(255, 255, 0))

            # 显示形状类型
            img.draw_string_advanced(mid_x - 25, by + bh//2, 18, "Triangle", color=(255, 165, 0))

    except Exception as e:
        shape_name = "Error"
        print("Inner shape detection error:", str(e))

def detect_circles_professional(img, roi):
    """基于参考代码的专业圆形检测方法"""
    try:
        print("🔵 Using professional circle detection method...")

        # 在ROI区域内进行圆形检测
        roi_img = img.copy(roi=roi)

        # 使用参考代码的find_circles方法和参数
        circles = roi_img.find_circles(threshold=6000)  # 参考代码的阈值

        print(f"------圆形统计开始------")
        detected_circles = []
        count = 0

        for circle in circles:
            # 转换回全图坐标
            circle_x = circle.x() + roi[0]
            circle_y = circle.y() + roi[1]
            radius = circle.r()
            diameter = radius * 2

            # 验证圆形是否在合理范围内
            if 10 < radius < 100 and circle_x > 0 and circle_y > 0:
                detected_circles.append({
                    'circle': circle,
                    'x': circle_x,
                    'y': circle_y,
                    'radius': radius,
                    'diameter': diameter
                })

                print(f"Circle {count}: center=({circle_x},{circle_y}), radius={radius}, diameter={diameter}")
                count += 1

        print("---------END---------")

        return detected_circles

    except Exception as e:
        print(f"Professional circle detection error: {e}")
        return []

def enhanced_circle_detection(img, target_blob, roi):
    """增强的圆形检测 - 结合专业方法和blob分析"""
    try:
        # 方法1：专业find_circles检测
        professional_circles = detect_circles_professional(img, roi)

        if len(professional_circles) > 0:
            # 选择最佳圆形（最接近blob中心的）
            bx, by = target_blob.x(), target_blob.y()
            blob_center_x = bx + target_blob.w() // 2
            blob_center_y = by + target_blob.h() // 2

            best_circle = min(professional_circles,
                            key=lambda c: abs(c['x'] - blob_center_x) + abs(c['y'] - blob_center_y))

            print(f"✓ Selected best professional circle: center=({best_circle['x']},{best_circle['y']}), diameter={best_circle['diameter']}")

            return best_circle

        else:
            # 方法2：备用blob估算
            print("⚠️ Professional detection failed, using blob estimation")

            bx, by, bw, bh = target_blob.x(), target_blob.y(), target_blob.w(), target_blob.h()
            area = target_blob.pixels()

            # 基于面积计算半径
            radius = int(math.sqrt(area / math.pi))
            diameter = radius * 2
            center_x = bx + bw // 2
            center_y = by + bh // 2

            return {
                'circle': None,
                'x': center_x,
                'y': center_y,
                'radius': radius,
                'diameter': diameter,
                'method': 'blob_estimation'
            }

    except Exception as e:
        print(f"Enhanced circle detection error: {e}")
        return None

def test_circle_detection_only(img):
    """只检测最大的一个圆形 - 解决卡顿问题"""
    global shape_name

    try:
        # 检测圆形，设置合理的半径范围
        circles = img.find_circles(threshold=2000, r_min=15, r_max=120)

        if len(circles) > 0:
            # 只选择最大的圆形
            largest_circle = max(circles, key=lambda c: c.r())

            circle_x = largest_circle.x()
            circle_y = largest_circle.y()
            radius = largest_circle.r()
            diameter = radius * 2

            # 只绘制最大的圆形
            img.draw_circle(circle_x, circle_y, radius, color=(0, 255, 0), thickness=3, fill=False)

            # 绘制直径线
            img.draw_line(circle_x - radius, circle_y, circle_x + radius, circle_y,
                         color=(255, 255, 0), thickness=2)
            img.draw_line(circle_x, circle_y - radius, circle_x, circle_y + radius,
                         color=(255, 255, 0), thickness=2)

            # 显示直径信息
            img.draw_string_advanced(circle_x - 25, circle_y - radius - 30, 20,
                                   f"D:{diameter}", color=(255, 255, 0))

            # 显示圆心坐标
            img.draw_string_advanced(circle_x - 20, circle_y + radius + 15, 16,
                                   f"({circle_x},{circle_y})", color=(150, 150, 150))

            shape_name = f"Circle_D{diameter}"
            print(f"Largest Circle: center=({circle_x},{circle_y}), radius={radius}, diameter={diameter}")

        else:
            # 如果没检测到，尝试更低阈值，但仍然只要最大的
            circles_low = img.find_circles(threshold=1000, r_min=10, r_max=150)

            if len(circles_low) > 0:
                largest_circle = max(circles_low, key=lambda c: c.r())

                circle_x = largest_circle.x()
                circle_y = largest_circle.y()
                radius = largest_circle.r()
                diameter = radius * 2

                # 用橙色标记低阈值检测的最大圆形
                img.draw_circle(circle_x, circle_y, radius, color=(255, 165, 0), thickness=2, fill=False)
                img.draw_string_advanced(circle_x - 20, circle_y - radius - 25, 18,
                                       f"D:{diameter}", color=(255, 165, 0))

                shape_name = f"LowT_D{diameter}"
                print(f"Low threshold largest: center=({circle_x},{circle_y}), radius={radius}")
            else:
                shape_name = "No_Circle"
                print("No circles detected")

    except Exception as e:
        print(f"Circle detection error: {e}")
        shape_name = "Error"

def detect_largest_circle_fast(img):
    """超快速圆形检测 - 只返回最大圆形数据"""
    try:
        # 快速检测，只要最大的
        circles = img.find_circles(threshold=2500, r_min=20, r_max=100)

        if len(circles) > 0:
            largest = max(circles, key=lambda c: c.r())
            return {
                'x': largest.x(),
                'y': largest.y(),
                'r': largest.r(),
                'd': largest.r() * 2,
                'found': True
            }
        else:
            # 快速备用检测
            circles_low = img.find_circles(threshold=1500, r_min=15, r_max=120)
            if len(circles_low) > 0:
                largest = max(circles_low, key=lambda c: c.r())
                return {
                    'x': largest.x(),
                    'y': largest.y(),
                    'r': largest.r(),
                    'd': largest.r() * 2,
                    'found': True,
                    'low_threshold': True
                }

        return None

    except Exception as e:
        print(f"Fast detection error: {e}")
        return None

def draw_cached_circle(img, result):
    """使用缓存结果快速绘制圆形"""
    try:
        x, y, r, d = result['x'], result['y'], result['r'], result['d']

        # 选择颜色
        color = (255, 165, 0) if result.get('low_threshold') else (0, 255, 0)

        # 最简绘制
        img.draw_circle(x, y, r, color=color, thickness=2, fill=False)
        img.draw_string_advanced(x - 15, y - r - 25, 18, f"D:{d}", color=color)

    except Exception as e:
        print(f"Draw error: {e}")

def draw_simple_status(img):
    """最简状态显示"""
    global shape_name
    try:
        if shape_name and "Circle" in shape_name:
            img.draw_string_advanced(10, 10, 18, "FOUND", color=(0, 255, 0))
        else:
            img.draw_string_advanced(10, 10, 18, "SEARCH", color=(255, 255, 0))
    except:
        pass

def draw_circle_test_info(img):
    """超简化显示 - 减少卡顿"""
    global shape_name

    try:
        # 只显示最基本的信息
        if shape_name and "Found" in shape_name:
            status_color = (0, 255, 0)
            status_text = f"FOUND: {shape_name}"
        else:
            status_color = (255, 255, 0)
            status_text = "SEARCHING..."

        # 简化显示
        img.draw_string_advanced(10, 10, 20, status_text, color=status_color)
        img.draw_string_advanced(10, 35, 16, "Black/Gray Circle Test", color=(150, 150, 150))

    except Exception as e:
        print(f"Display error: {e}")

def draw_results_on_lcd(img):
    """基于工作参考代码的绘制方法 - 直接在摄像头图像上绘制"""
    global outer_rect, inner_rect, shape_name, distances

    try:
        # 不要清除图像！直接在摄像头图像上绘制 - 这是关键修正！

        # 绘制画面中心十字线 (黄色) - 基于工作参考代码
        center_x = PICTURE_WIDTH // 2
        center_y = PICTURE_HEIGHT // 2
        img.draw_line(center_x - 20, center_y, center_x + 20, center_y, color=(255, 255, 0), thickness=2)
        img.draw_line(center_x, center_y - 20, center_x, center_y + 20, color=(255, 255, 0), thickness=2)

        # 绘制黑色边框 (红色轮廓) - 显示边框长宽
        if outer_rect:
            ox, oy, ow, oh = outer_rect.x(), outer_rect.y(), outer_rect.w(), outer_rect.h()

            # 绘制红色边框轮廓
            img.draw_rectangle(ox, oy, ow, oh, color=(255, 0, 0), thickness=5, fill=False)

            # 标注边框宽度 - 在上方
            img.draw_line(ox, oy - 15, ox + ow, oy - 15, color=(255, 0, 0), thickness=3)
            frame_width_text = "Frame W:{}".format(ow)
            img.draw_string_advanced(ox + ow//2 - 40, oy - 40, 22, frame_width_text, color=(255, 0, 0))

            # 标注边框高度 - 在左侧
            img.draw_line(ox - 15, oy, ox - 15, oy + oh, color=(255, 0, 0), thickness=3)
            frame_height_text = "Frame H:{}".format(oh)
            img.draw_string_advanced(ox - 90, oy + oh//2 - 11, 22, frame_height_text, color=(255, 0, 0))

            # 在边框右上角显示形状名称
            img.draw_string_advanced(ox + ow - 80, oy + 10, 20, shape_name, color=(0, 255, 255))

        # 优化的状态信息显示
        frame_status = "FOUND" if outer_rect else "SEARCHING"
        frame_color = (0, 255, 0) if outer_rect else (255, 255, 0)

        shape_status = shape_name if shape_name and shape_name != "None" else "DETECTING"
        shape_color = (0, 255, 0) if shape_name and shape_name not in ["None", "Error"] else (255, 255, 0)

        # 状态栏背景
        img.draw_rectangle(5, PICTURE_HEIGHT - 50, PICTURE_WIDTH - 10, 45, color=(0, 0, 0), thickness=2, fill=True)

        # 显示边框状态
        img.draw_string_advanced(10, PICTURE_HEIGHT - 45, 18, f"FRAME: {frame_status}", color=frame_color)

        # 显示形状状态
        img.draw_string_advanced(10, PICTURE_HEIGHT - 25, 18, f"SHAPE: {shape_status}", color=shape_color)

        # 显示系统版本和状态
        system_status = "ACTIVE" if outer_rect and shape_name != "None" else "SCANNING"
        system_color = (0, 255, 0) if system_status == "ACTIVE" else (255, 255, 0)

        img.draw_string_advanced(PICTURE_WIDTH - 150, PICTURE_HEIGHT - 35, 16, f"SYSTEM: {system_status}", color=system_color)

        # 优化的标题显示
        img.draw_string_advanced(10, 10, 22, "K230 Smart Recognition v2.0", color=(0, 255, 255))

        # 添加实时性能指示器
        current_time = time.time()
        if hasattr(draw_results_on_lcd, 'last_time'):
            fps = 1.0 / (current_time - draw_results_on_lcd.last_time) if current_time != draw_results_on_lcd.last_time else 0
            img.draw_string_advanced(PICTURE_WIDTH - 100, 10, 16, f"FPS: {fps:.1f}", color=(150, 150, 150))
        draw_results_on_lcd.last_time = current_time

    except Exception as e:
        print("Drawing error:", str(e))

def main():
    """K230距离测量系统主函数"""
    global sensor

    try:
        print("K230 Distance Measurement System Starting...")

        # 启用退出点
        os.exitpoint(os.EXITPOINT_ENABLE)

        # 初始化摄像头
        sensor = Sensor(width=PICTURE_WIDTH, height=PICTURE_HEIGHT)
        sensor.reset()
        sensor.set_framesize(width=PICTURE_WIDTH, height=PICTURE_HEIGHT)
        sensor.set_pixformat(Sensor.RGB565)
        print(f"Camera configured: {PICTURE_WIDTH}x{PICTURE_HEIGHT}")

        # 初始化LCD显示
        Display.init(Display.ST7701, to_ide=True)
        print("K230 LCD screen initialized (ST7701)")

        # 初始化媒体管理器
        MediaManager.init()
        print("Media manager initialized")

        # 启动摄像头
        sensor.run()
        print("Camera started")

        print("=== K230 Distance Measurement Ready ===")
        print(f"已知A4纸宽度: {KNOWN_WIDTH} inch ({KNOWN_WIDTH * 2.54:.1f} cm)")
        print(f"相机焦距: {FOCAL_LENGTH} pixels")

        frame_count = 0

        while True:
            os.exitpoint()

            try:
                # 获取图像
                img = sensor.snapshot()

                # 每5帧检测一次，提高响应性
                frame_count += 1
                if frame_count % 5 == 0:
                    # 检测A4纸标记
                    marker = find_marker(img)

                    if marker:
                        # 计算距离
                        distance = calculate_marker_distance(marker)

                        # 绘制标记和距离信息
                        draw_marker_and_distance(img, marker, distance)

                        print(f"检测成功: 距离 = {distance:.1f}cm")
                    else:
                        # 显示搜索状态
                        img.draw_string_advanced(10, 10, 20, "SEARCHING A4 MARKER...", color=(255, 255, 0))
                        img.draw_string_advanced(10, 35, 16, "Place A4 paper in view", color=(150, 150, 150))
                else:
                    # 非检测帧，只显示上次的结果
                    if marker_found and current_distance > 0:
                        # 显示缓存的距离信息
                        distance_text = f"Distance: {current_distance:.1f}cm"
                        img.draw_string_advanced(PICTURE_WIDTH - 200, PICTURE_HEIGHT - 30, 20,
                                               distance_text, color=(0, 255, 0))

                        distance_inches = current_distance / 2.54
                        inch_text = f"({distance_inches:.1f} inch)"
                        img.draw_string_advanced(PICTURE_WIDTH - 200, PICTURE_HEIGHT - 10, 16,
                                               inch_text, color=(150, 150, 150))

                # 显示系统信息
                img.draw_string_advanced(10, PICTURE_HEIGHT - 10, 14,
                                       "K230 Distance Measurement v1.0", color=(0, 255, 255))

                # 显示帧率信息
                if frame_count % 30 == 0:  # 每30帧更新一次帧率显示
                    current_time = time.time()
                    if hasattr(main, 'last_time'):
                        fps = 30.0 / (current_time - main.last_time) if current_time != main.last_time else 0
                        img.draw_string_advanced(PICTURE_WIDTH - 80, 10, 14, f"FPS:{fps:.1f}", color=(150, 150, 150))
                    main.last_time = current_time

                # 显示图像
                Display.show_image(img)

                # 控制帧率，平衡流畅性和处理性能
                time.sleep_ms(50)  # 约20FPS

            except Exception as e:
                print("Processing error:", str(e))
                # 显示错误信息
                img.draw_string_advanced(10, 10, 18, "ERROR OCCURRED", color=(255, 0, 0))
                img.draw_string_advanced(10, 35, 14, str(e)[:40], color=(255, 0, 0))
                Display.show_image(img)
                time.sleep_ms(1000)
                continue

    except KeyboardInterrupt as e:
        print("User stopped:", e)
    except BaseException as e:
        print("System error:", e)
    finally:
        # 清理资源
        try:
            if isinstance(sensor, Sensor):
                sensor.stop()
            Display.deinit()
            os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
            time.sleep_ms(100)
            MediaManager.deinit()
        except:
            pass
        print("System cleanup completed")

if __name__ == "__main__":
    main()
