# K230 Rectangle Recognition - Minimal Memory Version
# Ultra-optimized for K230 memory constraints

import time, os, sys
from media.sensor import *
from media.display import *
from media.media import *

# Minimal configuration
WIDTH = 160
HEIGHT = 120
SENSOR_ID = 2

# Global variables to save memory
outer_rect = None
inner_rect = None
shape_name = "None"

def find_rectangles(img):
    """Find rectangles with minimal memory usage"""
    global outer_rect, inner_rect
    
    try:
        # Direct binary processing on original image
        img.binary([(0, 90)])
        
        # Find rectangles with very low threshold
        rects = img.find_rects(threshold=500)
        
        if len(rects) >= 2:
            # Sort by area - largest first
            rects.sort(key=lambda r: r.w() * r.h(), reverse=True)
            outer_rect = rects[0]
            inner_rect = rects[1]
            return 2
        elif len(rects) == 1:
            outer_rect = rects[0]
            inner_rect = None
            return 1
        else:
            outer_rect = None
            inner_rect = None
            return 0
            
    except Exception as e:
        print("Find error:", str(e)[:20])  # Truncate error message
        return -1

def detect_inner_shape(img):
    """Minimal shape detection"""
    global inner_rect, shape_name
    
    if not inner_rect:
        shape_name = "None"
        return
        
    try:
        # Get inner area
        ix, iy, iw, ih = inner_rect.rect()
        
        # Skip if too small
        if iw < 10 or ih < 10:
            shape_name = "Small"
            return
            
        # Simple ROI
        roi = (ix + 2, iy + 2, iw - 4, ih - 4)
        
        # Try only circle detection to save memory
        circles = img.find_circles(roi=roi, threshold=1500, x_stride=4, y_stride=4)
        if len(circles) > 0:
            shape_name = "Circle"
            return
            
        # Try line detection
        lines = img.find_line_segments(roi=roi, merge_distance=20)
        if len(lines) == 3:
            shape_name = "Triangle"
        elif len(lines) == 4:
            shape_name = "Rect"
        elif len(lines) > 4:
            shape_name = "Multi"
        else:
            shape_name = "Unknown"
            
    except Exception as e:
        shape_name = "Error"

def draw_results(img):
    """Minimal drawing"""
    global outer_rect, inner_rect, shape_name
    
    try:
        # Draw outer rectangle
        if outer_rect:
            img.draw_rectangle(outer_rect.rect(), color=(255, 0, 0), thickness=1)
        
        # Draw inner rectangle
        if inner_rect:
            img.draw_rectangle(inner_rect.rect(), color=(0, 255, 0), thickness=1)
            
            # Draw shape name
            cx = inner_rect.x() + inner_rect.w() // 2
            cy = inner_rect.y() + inner_rect.h() // 2
            img.draw_string_advanced(cx - 15, cy, 12, shape_name, color=(0, 0, 255))
        
        # Simple status
        status = "O" if outer_rect else "X"
        status += "I" if inner_rect else "X"
        img.draw_string_advanced(2, 2, 10, status, color=(255, 255, 255))
        
    except Exception as e:
        # Silent fail to save memory
        pass

def main():
    sensor = None
    
    try:
        # Minimal sensor setup
        sensor = Sensor(id=SENSOR_ID)
        sensor.reset()
        sensor.set_framesize(width=WIDTH, height=HEIGHT, chn=CAM_CHN_ID_0)
        sensor.set_pixformat(Sensor.RGB565, chn=CAM_CHN_ID_0)
        
        # Minimal display
        Display.init(Display.ST7701, width=800, height=480, to_ide=True)
        
        # Start
        MediaManager.init()
        sensor.run()
        
        print("K230 Minimal Rect System")
        
        frame = 0
        while True:
            os.exitpoint()
            
            try:
                # Get image
                img = sensor.snapshot(chn=CAM_CHN_ID_0)
                
                # Process every 5th frame only
                if frame % 5 == 0:
                    rect_count = find_rectangles(img)
                    if rect_count >= 2:
                        detect_inner_shape(img)
                        print("Rects:", rect_count, "Shape:", shape_name)
                
                # Always draw
                draw_results(img)
                
                # Display centered
                x_offset = (800 - WIDTH) // 2
                y_offset = (480 - HEIGHT) // 2
                Display.show_image(img, x=x_offset, y=y_offset)
                
                frame += 1
                time.sleep_ms(150)  # Slow down to save memory
                
            except Exception as e:
                print("Frame err:", str(e)[:15])
                time.sleep_ms(300)
                
    except KeyboardInterrupt:
        print("Stopped by user")
    except Exception as e:
        print("System err:", str(e)[:20])
    finally:
        # Cleanup
        try:
            if sensor:
                sensor.stop()
            Display.deinit()
            MediaManager.deinit()
        except:
            pass
        print("Cleanup done")

if __name__ == "__main__":
    main()
