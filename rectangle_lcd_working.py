# K230 Rectangle Recognition - 基于完整参考代码的LCD显示版本
# Copyright: Mi Cu Electronics Studio
# Developer: Alex (Mike Team Engineer)
# 完全基于您提供的工作参考代码

import time
import os
import sys
import gc

from media.sensor import *
from media.display import *
from media.media import *

# 显示配置参数 - 基于参考代码
DETECT_WIDTH = ALIGN_UP(640, 16)
DETECT_HEIGHT = 480

# 矩形检测参数 - 基于参考代码
LAB_PARAMS = {
    # 黑色LAB阈值 - 基于参考代码
    'black_threshold': [(0, 30, -128, 127, -128, 127)],
    
    # 矩形参数
    'rect_min_area': 1000,
    'rect_max_area': 300000,
    
    # 边缘检测参数
    'edge_thickness': 5,
    'inner_offset': 10,
    'outer_offset': 10
}

class RectangleLCDDetector:
    def __init__(self):
        """初始化矩形LCD检测器"""
        self.sensor = None
        self.detected_blobs = []
        self.rectangles = []
        self.outer_rect = None
        self.inner_rect = None
        self.shape_name = "None"
        self.distances = [0, 0, 0, 0]  # left, right, top, bottom

    def init_system(self):
        """初始化系统 - 完全基于参考代码"""
        print("🎯 Initializing Rectangle LCD Detection System...")
        print(f"🔬 LAB Threshold: {LAB_PARAMS['black_threshold'][0]}")

        # 初始化摄像头 - 基于参考代码
        self.sensor = Sensor(width=DETECT_WIDTH, height=DETECT_HEIGHT)
        self.sensor.reset()
        self.sensor.set_framesize(width=DETECT_WIDTH, height=DETECT_HEIGHT)
        self.sensor.set_pixformat(Sensor.RGB565)

        # 初始化显示 - 完全基于参考代码的LCD显示方式 (关键修正！)
        Display.init(Display.ST7701, to_ide=True)  # 不添加width和height参数
        print("✅ LCD Display initialized (ST7701) - Camera feed should be visible")

        # 初始化媒体管理器 - 基于参考代码
        MediaManager.init()
        self.sensor.run()

        print("✅ Rectangle LCD Detection System initialized!")

    def find_rectangles(self, img):
        """基于参考代码的矩形检测方法"""
        try:
            # 使用参考代码的LAB阈值检测黑色区域
            black_blobs = img.find_blobs(LAB_PARAMS['black_threshold'],
                                        pixels_threshold=50,
                                        area_threshold=50,
                                        merge=True)

            self.detected_blobs = black_blobs
            
            if len(black_blobs) >= 2:
                # 按面积排序，最大的是外框，第二大的是内框
                blobs_sorted = sorted(black_blobs, key=lambda b: b.pixels(), reverse=True)
                self.outer_rect = blobs_sorted[0]
                self.inner_rect = blobs_sorted[1]
                print("Found outer and inner rectangles")
                return 2
            elif len(black_blobs) == 1:
                self.outer_rect = black_blobs[0]
                self.inner_rect = None
                print("Found single rectangle")
                return 1
            else:
                self.outer_rect = None
                self.inner_rect = None
                return 0
                
        except Exception as e:
            print("Rectangle detection error:", str(e))
            return -1

    def calculate_distances(self):
        """计算内外边框距离"""
        if not self.outer_rect or not self.inner_rect:
            self.distances = [0, 0, 0, 0]
            return
            
        try:
            # 使用blob对象的方法获取边界
            ox, oy, ow, oh = self.outer_rect.x(), self.outer_rect.y(), self.outer_rect.w(), self.outer_rect.h()
            ix, iy, iw, ih = self.inner_rect.x(), self.inner_rect.y(), self.inner_rect.w(), self.inner_rect.h()
            
            self.distances[0] = abs(ix - ox)  # left
            self.distances[1] = abs((ox + ow) - (ix + iw))  # right
            self.distances[2] = abs(iy - oy)  # top
            self.distances[3] = abs((oy + oh) - (iy + ih))  # bottom
            
            print("Distances - L:{} R:{} T:{} B:{}".format(self.distances[0], self.distances[1], self.distances[2], self.distances[3]))
            
        except Exception as e:
            print("Distance calculation error:", str(e))

    def detect_inner_shape(self, img):
        """基于参考代码的形状检测"""
        if not self.inner_rect:
            self.shape_name = "None"
            return
            
        try:
            ix, iy, iw, ih = self.inner_rect.x(), self.inner_rect.y(), self.inner_rect.w(), self.inner_rect.h()
            
            # Skip if too small
            if iw < 20 or ih < 20:
                self.shape_name = "Small"
                return
                
            # 设置ROI区域
            roi = (ix + 5, iy + 5, iw - 10, ih - 10)
            
            # 参考代码的线段检测方法
            img_line = img.to_rgb565(copy=True)
            img_line.midpoint_pool(2, 2)  # 降采样减少计算量
            
            # 在ROI区域内寻找线段
            lines = img_line.find_line_segments(roi, 10, 10)
            
            # 过滤短线段
            valid_lines = [line for line in lines if line.length() > 15]
            line_count = len(valid_lines)
            
            # 尝试圆形检测
            circles = img.find_circles(roi=roi, threshold=2000, x_stride=2, y_stride=2)
            
            if len(circles) > 0:
                self.shape_name = "Circle"
            elif line_count == 3:
                self.shape_name = "Triangle"
            elif line_count == 4:
                self.shape_name = "Rectangle"
            elif line_count > 4:
                self.shape_name = "Polygon"
            else:
                self.shape_name = "Unknown"
                
            print("Shape detected: {} (Lines: {}, Circles: {})".format(self.shape_name, line_count, len(circles)))
            
        except Exception as e:
            self.shape_name = "Error"
            print("Shape detection error:", str(e))

    def draw_results(self, img):
        """基于参考代码的绘制方法"""
        try:
            # 绘制所有检测到的色块 (蓝色边框) - 基于参考代码
            for blob in self.detected_blobs:
                img.draw_rectangle(blob.x(), blob.y(), blob.w(), blob.h(),
                                 color=(0, 0, 255), thickness=1, fill=False)

            # 绘制画面中心十字线 (黄色) - 基于参考代码
            center_x = DETECT_WIDTH // 2
            center_y = DETECT_HEIGHT // 2
            img.draw_line(center_x - 20, center_y, center_x + 20, center_y, color=(255, 255, 0), thickness=2)
            img.draw_line(center_x, center_y - 20, center_x, center_y + 20, color=(255, 255, 0), thickness=2)

            # 绘制外边框 (红色) - 基于参考代码风格
            if self.outer_rect:
                img.draw_rectangle(self.outer_rect.x(), self.outer_rect.y(), self.outer_rect.w(), self.outer_rect.h(),
                                 color=(255, 0, 0), thickness=4, fill=False)

            # 绘制内边框 (绿色) - 基于参考代码风格
            if self.inner_rect:
                img.draw_rectangle(self.inner_rect.x(), self.inner_rect.y(), self.inner_rect.w(), self.inner_rect.h(),
                                 color=(0, 255, 0), thickness=3, fill=False)

                # 在内边框中心绘制形状名称
                cx = self.inner_rect.x() + self.inner_rect.w() // 2
                cy = self.inner_rect.y() + self.inner_rect.h() // 2
                img.draw_string_advanced(cx - 30, cy, 24, self.shape_name, color=(0, 0, 255))

                # 绘制距离信息
                if self.distances[0] > 0:
                    ox, oy, ow, oh = self.outer_rect.x(), self.outer_rect.y(), self.outer_rect.w(), self.outer_rect.h()
                    img.draw_string_advanced(ox + 10, oy + 30, 16, "L:{}".format(self.distances[0]), color=(255, 255, 0))
                    img.draw_string_advanced(ox + ow - 60, oy + 30, 16, "R:{}".format(self.distances[1]), color=(255, 255, 0))
                    img.draw_string_advanced(ox + ow // 2 - 20, oy + 10, 16, "T:{}".format(self.distances[2]), color=(255, 255, 0))
                    img.draw_string_advanced(ox + ow // 2 - 20, oy + oh - 25, 16, "B:{}".format(self.distances[3]), color=(255, 255, 0))

            # 状态信息 - 基于参考代码风格
            status = "Outer:{} Inner:{} Shape:{}".format(
                "YES" if self.outer_rect else "NO",
                "YES" if self.inner_rect else "NO",
                self.shape_name
            )
            img.draw_string_advanced(10, DETECT_HEIGHT - 40, 16, status, color=(255, 255, 255))

        except Exception as e:
            print("Drawing error:", str(e))

    def run(self):
        """运行检测主循环 - 基于参考代码"""
        print("🎯 Starting Rectangle LCD Detection...")
        print(f"🔬 LAB Threshold: {LAB_PARAMS['black_threshold'][0]}")
        print("⌨️ Press Ctrl+C to stop")

        fps_counter = time.clock()
        frame_count = 0

        try:
            while True:
                fps_counter.tick()
                os.exitpoint()
                frame_count += 1

                # 获取图像 - 基于参考代码
                img = self.sensor.snapshot()

                # 执行矩形检测
                rect_count = self.find_rectangles(img)
                if rect_count >= 2:
                    self.calculate_distances()
                    self.detect_inner_shape(img)

                # 绘制结果
                self.draw_results(img)

                # 显示FPS和状态 - 基于参考代码风格
                status = "Found" if self.outer_rect else "None"
                fps_text = f"FPS: {fps_counter.fps():.1f} | {status}"
                img.draw_string_advanced(DETECT_WIDTH-200, 20, 16, fps_text, color=(255, 255, 0))

                # 显示标题
                img.draw_string_advanced(10, 20, 16, "K230 Rectangle LCD System", color=(0, 255, 255))

                # 显示图像 - 基于参考代码
                Display.show_image(img)
                gc.collect()

        except KeyboardInterrupt:
            print("\n🛑 Detection stopped")
        except Exception as e:
            print(f"❌ Error: {e}")

    def cleanup(self):
        """清理资源 - 基于参考代码"""
        print("🧹 Cleaning up...")
        if self.sensor:
            self.sensor.stop()
        Display.deinit()
        os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
        time.sleep_ms(100)
        MediaManager.deinit()
        print("✅ Detector stopped.")

def main():
    """主函数 - 基于参考代码"""
    os.exitpoint(os.EXITPOINT_ENABLE)

    print("🎯 K230 Rectangle LCD Detection System")
    print("📅 Version 1.0 - Based on Working Reference Code")
    print("🏭 Powered by 米醋电子工作室")
    print(f"🔬 LAB Threshold: {LAB_PARAMS['black_threshold'][0]}")

    detector = RectangleLCDDetector()

    try:
        detector.init_system()
        detector.run()
    except Exception as e:
        print(f"💥 Error: {e}")
    finally:
        detector.cleanup()

if __name__ == "__main__":
    main()
