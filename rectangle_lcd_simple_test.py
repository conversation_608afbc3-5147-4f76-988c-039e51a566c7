# K230 Rectangle Recognition - LCD显示简化测试版本
# Copyright: Mi Cu Electronics Studio
# Developer: <PERSON> (Mike Team Engineer)
# 基于参考代码的简化测试版本，确保LCD显示正常

import time
import os
import sys
import image
from media.sensor import *
from media.display import *
from media.media import *

# 显示模式选择 - 基于参考代码
DISPLAY_MODE = "LCD"

# 根据模式设置显示宽高 - 完全按照参考代码
if DISPLAY_MODE == "LCD":
    # 3.1寸屏幕模式
    DISPLAY_WIDTH = 800
    DISPLAY_HEIGHT = 480
else:
    raise ValueError("当前只支持LCD模式")

# 摄像头配置
PICTURE_WIDTH = 640
PICTURE_HEIGHT = 480

# 全局变量
sensor = None
outer_rect = None
inner_rect = None
shape_name = "None"

def find_rectangles_simple(img):
    """简化的矩形检测"""
    global outer_rect, inner_rect
    
    try:
        # 黑色阈值
        black_threshold = [(0, 30, -128, 127, -128, 127)]
        
        # 寻找黑色色块
        blobs = img.find_blobs(black_threshold, False, 
                              (0, 0, img.width()//2, img.height()//2), 
                              x_stride=2, y_stride=2, 
                              pixels_threshold=500, margin=True)
        
        if len(blobs) >= 2:
            # 按面积排序
            blobs_sorted = sorted(blobs, key=lambda b: b.pixels(), reverse=True)
            outer_rect = blobs_sorted[0]
            inner_rect = blobs_sorted[1]
            return 2
        elif len(blobs) == 1:
            outer_rect = blobs[0]
            inner_rect = None
            return 1
        else:
            outer_rect = None
            inner_rect = None
            return 0
            
    except Exception as e:
        print("Detection error:", str(e))
        return -1

def draw_simple_results(lcd_img, camera_img):
    """简化的LCD绘制方法"""
    global outer_rect, inner_rect, shape_name
    
    try:
        # 清除LCD图像内容
        lcd_img.clear()
        
        # 将摄像头图像绘制到LCD图像上 (居中显示)
        x_offset = (DISPLAY_WIDTH - PICTURE_WIDTH) // 2
        y_offset = 0
        
        # 复制摄像头图像
        lcd_img.draw_image(camera_img, x_offset, y_offset)
        
        # 绘制检测结果
        if outer_rect:
            # 调整坐标到LCD显示区域
            ox = outer_rect.x() + x_offset
            oy = outer_rect.y() + y_offset
            ow = outer_rect.w()
            oh = outer_rect.h()
            
            # 绘制外边框 (红色)
            lcd_img.draw_rectangle(ox, oy, ow, oh, color=(255, 0, 0), thickness=5, fill=False)

        if inner_rect:
            # 调整坐标到LCD显示区域
            ix = inner_rect.x() + x_offset
            iy = inner_rect.y() + y_offset
            iw = inner_rect.w()
            ih = inner_rect.h()
            
            # 绘制内边框 (绿色)
            lcd_img.draw_rectangle(ix, iy, iw, ih, color=(0, 255, 0), thickness=4, fill=False)

            # 在内边框中心绘制形状名称
            cx = ix + iw // 2
            cy = iy + ih // 2
            lcd_img.draw_string_advanced(cx - 30, cy, 32, "RECT", color=(0, 0, 255))

        # LCD屏幕标题
        lcd_img.draw_string_advanced(30, 30, 40, "K230 LCD Test", color=(0, 255, 255))
        
        # 状态信息
        status = "Outer:{} Inner:{}".format(
            "YES" if outer_rect else "NO",
            "YES" if inner_rect else "NO"
        )
        lcd_img.draw_string_advanced(30, DISPLAY_HEIGHT - 60, 28, status, color=(255, 255, 255))

    except Exception as e:
        print("Drawing error:", str(e))

# 主程序 - 简化版本
def rectangle_lcd_test():
    global sensor
    
    print("=== K230 Rectangle LCD Test ===")
    
    try:
        # 启用退出点
        os.exitpoint(os.EXITPOINT_ENABLE)

        # 创建用于LCD绘图的图像对象
        lcd_img = image.Image(DISPLAY_WIDTH, DISPLAY_HEIGHT, image.ARGB8888)
        print("LCD image created: {}x{}".format(DISPLAY_WIDTH, DISPLAY_HEIGHT))

        # 初始化显示器
        Display.init(Display.ST7701, width=DISPLAY_WIDTH, height=DISPLAY_HEIGHT, to_ide=True)
        print("LCD display initialized")

        # 初始化媒体管理器
        MediaManager.init()
        print("Media manager initialized")
        
        # 初始化摄像头
        sensor = Sensor(width=PICTURE_WIDTH, height=PICTURE_HEIGHT)
        sensor.reset()
        sensor.set_framesize(width=PICTURE_WIDTH, height=PICTURE_HEIGHT)
        sensor.set_pixformat(Sensor.RGB565)
        sensor.run()
        print("Camera initialized: {}x{}".format(PICTURE_WIDTH, PICTURE_HEIGHT))

        print("System ready - LCD should show camera feed now!")
        
        frame_count = 0
        while True:
            try:
                # 获取摄像头图像
                camera_img = sensor.snapshot(chn=CAM_CHN_ID_0)
                
                # 每5帧检测一次，减少计算负担
                if frame_count % 5 == 0:
                    rect_count = find_rectangles_simple(camera_img)
                    print("Frame {}: Found {} rectangles".format(frame_count, rect_count))

                # 在LCD图像上绘制结果
                draw_simple_results(lcd_img, camera_img)
                
                # 添加帧计数信息
                lcd_img.draw_string_advanced(30, 80, 24, "Frame: {}".format(frame_count), color=(255, 255, 0))

                # 显示到LCD屏幕
                Display.show_image(lcd_img)

                frame_count += 1
                time.sleep_ms(100)  # 控制刷新率
                os.exitpoint()  # 退出点
                
            except Exception as e:
                print("Frame error:", str(e))
                time.sleep_ms(200)
                continue
                
    except KeyboardInterrupt as e:
        print("用户终止：", e)
    except BaseException as e:
        print("系统异常：{}".format(e))
    finally:
        # 清理资源
        try:
            if isinstance(sensor, Sensor):
                sensor.stop()
                print("Camera stopped")
            Display.deinit()
            print("Display deinitialized")
            os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
            time.sleep_ms(100)
            MediaManager.deinit()
            print("Media manager deinitialized")
        except Exception as e:
            print("Cleanup error:", str(e))
        print("System stopped")

# 主程序入口
if __name__ == "__main__":
    rectangle_lcd_test()
