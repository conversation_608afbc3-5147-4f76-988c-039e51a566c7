# K230 摄像头LCD显示测试 - 基于工作参考代码
# Copyright: Mi Cu Electronics Studio
# Developer: <PERSON> (Mike Team Engineer)
# 专门测试摄像头画面是否能在LCD上显示

import time
import os
import sys
import gc

from media.sensor import *
from media.display import *
from media.media import *

# 显示配置参数 - 完全基于工作参考代码
DETECT_WIDTH = ALIGN_UP(640, 16)
DETECT_HEIGHT = 480

def camera_lcd_test():
    """摄像头LCD显示测试"""
    print("🎯 K230 Camera LCD Display Test")
    print("📅 Based on Working Reference Code")
    print("🏭 Powered by 米醋电子工作室")
    
    sensor = None
    
    try:
        # 启用退出点 - 基于工作参考代码
        os.exitpoint(os.EXITPOINT_ENABLE)
        
        print("🔧 Initializing camera...")
        
        # 初始化摄像头 - 完全基于工作参考代码
        sensor = Sensor(width=DETECT_WIDTH, height=DETECT_HEIGHT)
        sensor.reset()
        sensor.set_framesize(width=DETECT_WIDTH, height=DETECT_HEIGHT)
        sensor.set_pixformat(Sensor.RGB565)
        print(f"✅ Camera initialized: {DETECT_WIDTH}x{DETECT_HEIGHT}")

        print("🖥️ Initializing LCD display...")
        
        # 初始化显示 - 完全基于工作参考代码 (关键修正！)
        Display.init(Display.ST7701, to_ide=True)  # 不添加width和height参数
        print("✅ LCD Display initialized (ST7701)")

        print("📱 Initializing media manager...")
        
        # 初始化媒体管理器 - 基于工作参考代码
        MediaManager.init()
        print("✅ Media manager initialized")
        
        # 启动摄像头 - 基于工作参考代码
        sensor.run()
        print("✅ Camera started")

        print("🎯 System ready - LCD should show camera feed now!")
        print("⌨️ Press Ctrl+C to stop")
        
        # FPS计数器
        fps_counter = time.clock()
        frame_count = 0

        while True:
            fps_counter.tick()
            os.exitpoint()
            frame_count += 1

            try:
                # 获取摄像头图像 - 基于工作参考代码
                img = sensor.snapshot()
                
                # 在图像上绘制测试信息
                img.draw_string_advanced(10, 10, 32, "K230 Camera Test", color=(255, 255, 0))
                img.draw_string_advanced(10, 50, 24, f"Frame: {frame_count}", color=(0, 255, 255))
                img.draw_string_advanced(10, 80, 24, f"FPS: {fps_counter.fps():.1f}", color=(255, 0, 0))
                img.draw_string_advanced(10, 110, 20, f"Resolution: {DETECT_WIDTH}x{DETECT_HEIGHT}", color=(255, 255, 255))
                
                # 绘制画面中心十字线
                center_x = DETECT_WIDTH // 2
                center_y = DETECT_HEIGHT // 2
                img.draw_line(center_x - 30, center_y, center_x + 30, center_y, color=(0, 255, 0), thickness=3)
                img.draw_line(center_x, center_y - 30, center_x, center_y + 30, color=(0, 255, 0), thickness=3)
                
                # 绘制边框
                img.draw_rectangle(5, 5, DETECT_WIDTH - 10, DETECT_HEIGHT - 10, color=(255, 0, 255), thickness=2, fill=False)
                
                # 显示图像到LCD屏幕 - 基于工作参考代码
                Display.show_image(img)
                
                # 内存管理 - 基于工作参考代码
                gc.collect()
                
                # 每100帧打印一次状态
                if frame_count % 100 == 0:
                    print(f"📊 Frame {frame_count}: FPS={fps_counter.fps():.1f}")
                
            except Exception as e:
                print(f"⚠️ Frame processing error: {e}")
                time.sleep_ms(100)
                continue

    except KeyboardInterrupt:
        print("\n🛑 Test stopped by user")
    except Exception as e:
        print(f"❌ System error: {e}")
        import sys
        sys.print_exception(e)
    finally:
        # 清理资源 - 基于工作参考代码
        print("🧹 Cleaning up...")
        try:
            if sensor:
                sensor.stop()
                print("✅ Camera stopped")
            Display.deinit()
            print("✅ Display deinitialized")
            os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
            time.sleep_ms(100)
            MediaManager.deinit()
            print("✅ Media manager deinitialized")
        except Exception as e:
            print(f"⚠️ Cleanup error: {e}")
        print("✅ Test completed")

if __name__ == "__main__":
    camera_lcd_test()
