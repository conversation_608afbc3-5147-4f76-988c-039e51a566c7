# K230 Rectangle Recognition - K230 LCD屏幕修正版本
# Copyright: Mi Cu Electronics Studio
# Developer: <PERSON> (Mike Team Engineer)
# 基于参考代码修正K230屏幕显示问题

import time
import os
import sys
from media.sensor import *
from media.display import *
from media.media import *

# K230屏幕显示配置 - 基于参考代码
DISPLAY_MODE = "LCD"  # K230 3.1寸屏幕模式
DISPLAY_WIDTH = 800
DISPLAY_HEIGHT = 480

# 摄像头配置
PICTURE_WIDTH = 640
PICTURE_HEIGHT = 480

# 全局变量
sensor = None
outer_rect = None
inner_rect = None
shape_name = "None"
distances = [0, 0, 0, 0]  # left, right, top, bottom

def find_rectangles(img):
    """基于参考代码的矩形检测方法"""
    global outer_rect, inner_rect
    
    try:
        # 使用参考代码的方式 - 寻找色块来检测黑色矩形
        # 黑色阈值 - 检测黑色区域
        black_threshold = [(0, 30, -128, 127, -128, 127)]
        
        # 寻找黑色色块，参数参考示例代码
        blobs = img.find_blobs(black_threshold, False, 
                              (0, 0, img.width()//2, img.height()//2), 
                              x_stride=2, y_stride=2, 
                              pixels_threshold=1000, margin=True)
        
        if len(blobs) >= 2:
            # 按面积排序，最大的是外框，第二大的是内框
            blobs_sorted = sorted(blobs, key=lambda b: b.pixels(), reverse=True)
            outer_rect = blobs_sorted[0]
            inner_rect = blobs_sorted[1]
            print("Found outer and inner rectangles")
            return 2
        elif len(blobs) == 1:
            outer_rect = blobs[0]
            inner_rect = None
            print("Found single rectangle")
            return 1
        else:
            outer_rect = None
            inner_rect = None
            return 0
            
    except Exception as e:
        print("Rectangle detection error:", str(e))
        return -1

def calculate_distances():
    """计算内外边框距离"""
    global outer_rect, inner_rect, distances
    
    if not outer_rect or not inner_rect:
        distances = [0, 0, 0, 0]
        return
        
    try:
        # 使用blob对象的方法获取边界
        ox, oy, ow, oh = outer_rect.x(), outer_rect.y(), outer_rect.w(), outer_rect.h()
        ix, iy, iw, ih = inner_rect.x(), inner_rect.y(), inner_rect.w(), inner_rect.h()
        
        distances[0] = abs(ix - ox)  # left
        distances[1] = abs((ox + ow) - (ix + iw))  # right
        distances[2] = abs(iy - oy)  # top
        distances[3] = abs((oy + oh) - (iy + ih))  # bottom
        
        print("Distances - L:{} R:{} T:{} B:{}".format(distances[0], distances[1], distances[2], distances[3]))
        
    except Exception as e:
        print("Distance calculation error:", str(e))

def detect_inner_shape(img):
    """基于参考代码的形状检测"""
    global inner_rect, shape_name
    
    if not inner_rect:
        shape_name = "None"
        return
        
    try:
        ix, iy, iw, ih = inner_rect.x(), inner_rect.y(), inner_rect.w(), inner_rect.h()
        
        # Skip if too small
        if iw < 20 or ih < 20:
            shape_name = "Small"
            return
            
        # 设置ROI区域
        roi = (ix + 5, iy + 5, iw - 10, ih - 10)
        
        # 参考代码的线段检测方法
        img_line = img.to_rgb565(copy=True)
        img_line.midpoint_pool(2, 2)  # 降采样减少计算量
        
        # 在ROI区域内寻找线段
        lines = img_line.find_line_segments(roi, 10, 10)
        
        # 过滤短线段
        valid_lines = [line for line in lines if line.length() > 15]
        line_count = len(valid_lines)
        
        # 尝试圆形检测
        circles = img.find_circles(roi=roi, threshold=2000, x_stride=2, y_stride=2)
        
        if len(circles) > 0:
            shape_name = "Circle"
        elif line_count == 3:
            shape_name = "Triangle"
        elif line_count == 4:
            shape_name = "Rectangle"
        elif line_count > 4:
            shape_name = "Polygon"
        else:
            shape_name = "Unknown"
            
        print("Shape detected: {} (Lines: {}, Circles: {})".format(shape_name, line_count, len(circles)))
        
    except Exception as e:
        shape_name = "Error"
        print("Shape detection error:", str(e))

def draw_results_for_k230_lcd(img):
    """专门为K230 LCD屏幕优化的绘制方法"""
    global outer_rect, inner_rect, shape_name, distances
    
    try:
        # 绘制外边框 (红色) - K230 LCD屏幕优化
        if outer_rect:
            img.draw_rectangle(outer_rect.x(), outer_rect.y(), outer_rect.w(), outer_rect.h(), 
                             color=(255, 0, 0), thickness=4, fill=False)
            
        # 绘制内边框 (绿色)
        if inner_rect:
            img.draw_rectangle(inner_rect.x(), inner_rect.y(), inner_rect.w(), inner_rect.h(), 
                             color=(0, 255, 0), thickness=3, fill=False)
            
            # 在内边框中心绘制形状名称
            cx = inner_rect.x() + inner_rect.w() // 2
            cy = inner_rect.y() + inner_rect.h() // 2
            img.draw_string_advanced(cx - 30, cy, 24, shape_name, color=(0, 0, 255))
            
            # 绘制距离信息
            if distances[0] > 0:
                # 左边距离
                img.draw_string_advanced(outer_rect.x() + 5, outer_rect.y() + 25, 
                                       20, "L:{}".format(distances[0]), color=(255, 255, 0))
                # 右边距离
                img.draw_string_advanced(outer_rect.x() + outer_rect.w() - 60, outer_rect.y() + 25, 
                                       20, "R:{}".format(distances[1]), color=(255, 255, 0))
                # 上边距离
                img.draw_string_advanced(outer_rect.x() + outer_rect.w() // 2 - 20, outer_rect.y() + 5, 
                                       20, "T:{}".format(distances[2]), color=(255, 255, 0))
                # 下边距离
                img.draw_string_advanced(outer_rect.x() + outer_rect.w() // 2 - 20, 
                                       outer_rect.y() + outer_rect.h() - 25, 
                                       20, "B:{}".format(distances[3]), color=(255, 255, 0))
        
        # K230 LCD状态信息显示
        status = "Outer:{} Inner:{} Shape:{}".format(
            "YES" if outer_rect else "NO",
            "YES" if inner_rect else "NO", 
            shape_name
        )
        img.draw_string_advanced(10, img.height() - 40, 18, status, color=(255, 255, 255))
        
    except Exception as e:
        print("Drawing error:", str(e))

# 主程序 - 完全基于参考代码的K230 LCD显示
try:
    print("=== K230 Rectangle Recognition - LCD Display ===")
    
    # 启用退出点
    os.exitpoint(os.EXITPOINT_ENABLE)

    # 参考代码的摄像头初始化方式
    sensor = Sensor(width=PICTURE_WIDTH, height=PICTURE_HEIGHT)
    sensor.reset()
    sensor.set_framesize(width=PICTURE_WIDTH, height=PICTURE_HEIGHT)
    sensor.set_pixformat(Sensor.RGB565)

    # 根据参考代码修正K230屏幕显示初始化
    if DISPLAY_MODE == "LCD":
        # K230 3.1寸屏幕模式 - 这是关键修正！
        Display.init(Display.ST7701, width=DISPLAY_WIDTH, height=DISPLAY_HEIGHT, to_ide=True)
        print("K230 LCD screen initialized (800x480)")
    elif DISPLAY_MODE == "HDMI":
        # HDMI扩展板模式
        Display.init(Display.LT9611, width=DISPLAY_WIDTH, height=DISPLAY_HEIGHT, to_ide=True)
        print("HDMI display initialized")
    else:
        # 默认虚拟显示器
        Display.init(Display.VIRT, width=DISPLAY_WIDTH, height=DISPLAY_HEIGHT, fps=60)
        print("Virtual display initialized")
    
    # 初始化媒体管理器
    MediaManager.init()
    # 启动 sensor
    sensor.run()

    print("K230 LCD display system ready!")

    while True:
        os.exitpoint()
        img = sensor.snapshot(chn=CAM_CHN_ID_0)

        # 执行矩形检测
        rect_count = find_rectangles(img)
        if rect_count >= 2:
            calculate_distances()
            detect_inner_shape(img)

        # 使用K230 LCD优化的绘制方法
        draw_results_for_k230_lcd(img)

        # 显示系统信息 - K230 LCD专用
        img.draw_string_advanced(10, 10, 28, "K230 LCD Rectangle System", color=(0, 255, 255))
        img.draw_string_advanced(10, 45, 20, "LCD Display: 800x480", color=(255, 255, 0))
        
        # 显示到K230 LCD屏幕
        Display.show_image(img)
        
        time.sleep_ms(50)  # 控制刷新率

except KeyboardInterrupt as e:
    print("用户停止: ", e)
except BaseException as e:
    print("异常: {}".format(e))
finally:
    if isinstance(sensor, Sensor):
        sensor.stop()
    Display.deinit()
    os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
    time.sleep_ms(100)
    MediaManager.deinit()
    print("K230 LCD display system stopped")
