# K230矩形识别系统 - 使用摄像头原生分辨率
# 解决 sensor snapshot failed 问题
# 使用摄像头检测到的原生分辨率 1280x960

import time
import os
import sys
import gc

from media.sensor import *
from media.display import *
from media.media import *

# 使用摄像头原生分辨率 - 基于错误信息显示的分辨率
DETECT_WIDTH = 1280  # 摄像头原生宽度
DETECT_HEIGHT = 960  # 摄像头原生高度

def native_resolution_test():
    """使用原生分辨率的矩形检测测试"""
    print("🎯 K230 Native Resolution Rectangle Test")
    print("📅 Using Camera Native Resolution: 1280x960")
    print("🔧 Solving sensor snapshot failed issue")
    
    sensor = None
    
    try:
        # 启用退出点 - 基于您的工作参考代码
        os.exitpoint(os.EXITPOINT_ENABLE)
        
        print("🔧 Step 1: Initializing camera with native resolution...")
        
        # 使用摄像头原生分辨率初始化
        sensor = Sensor(width=DETECT_WIDTH, height=DETECT_HEIGHT)
        sensor.reset()
        sensor.set_framesize(width=DETECT_WIDTH, height=DETECT_HEIGHT)
        sensor.set_pixformat(Sensor.RGB565)
        print(f"✅ Camera configured: {DETECT_WIDTH}x{DETECT_HEIGHT} (Native)")

        print("🖥️ Step 2: Initializing LCD display...")
        
        # 初始化显示 - 完全基于您的工作参考代码
        Display.init(Display.ST7701, to_ide=True)
        print("✅ LCD Display initialized (ST7701)")

        print("📱 Step 3: Initializing media manager...")
        
        # 初始化媒体管理器 - 基于您的工作参考代码
        MediaManager.init()
        print("✅ Media manager initialized")
        
        print("🚀 Step 4: Starting camera...")
        
        # 启动摄像头 - 基于您的工作参考代码
        sensor.run()
        print("✅ Camera started")

        print("🎯 System Ready - Testing native resolution snapshot...")
        print("⌨️ Press Ctrl+C to stop")
        
        # FPS计数器
        fps_counter = time.clock()
        frame_count = 0
        success_count = 0
        error_count = 0

        while True:
            fps_counter.tick()
            os.exitpoint()
            frame_count += 1

            try:
                # 测试摄像头快照获取 - 使用原生分辨率
                img = sensor.snapshot()
                success_count += 1
                
                # 由于分辨率较大，降采样以提高性能
                img_small = img.to_rgb565(copy=True)
                img_small.midpoint_pool(4, 4)  # 降采样到320x240
                
                # 简单的矩形检测测试
                black_threshold = [(0, 30, -128, 127, -128, 127)]
                blobs = img_small.find_blobs(black_threshold, pixels_threshold=20, area_threshold=20, merge=True)
                
                # 在原图上绘制检测结果 (坐标需要放大4倍)
                for blob in blobs:
                    x, y, w, h = blob.x() * 4, blob.y() * 4, blob.w() * 4, blob.h() * 4
                    img.draw_rectangle(x, y, w, h, color=(255, 0, 0), thickness=6, fill=False)
                
                # 绘制测试信息
                img.draw_string_advanced(20, 20, 32, "Native Resolution Test", color=(0, 255, 0))
                img.draw_string_advanced(20, 60, 24, f"Frame: {frame_count}", color=(255, 255, 0))
                img.draw_string_advanced(20, 90, 24, f"Success: {success_count}", color=(0, 255, 255))
                img.draw_string_advanced(20, 120, 24, f"Errors: {error_count}", color=(255, 0, 0))
                img.draw_string_advanced(20, 150, 24, f"Blobs: {len(blobs)}", color=(255, 255, 255))
                img.draw_string_advanced(20, 180, 24, f"Resolution: {DETECT_WIDTH}x{DETECT_HEIGHT}", color=(255, 255, 255))
                
                # 显示FPS
                fps_text = f"FPS: {fps_counter.fps():.1f}"
                img.draw_string_advanced(DETECT_WIDTH-200, 30, 24, fps_text, color=(255, 255, 0))
                
                # 显示图像到LCD屏幕 - 基于您的工作参考代码
                Display.show_image(img)
                
                # 内存管理 - 基于您的工作参考代码
                gc.collect()
                
                # 每30帧打印一次状态 (降低频率以提高性能)
                if frame_count % 30 == 0:
                    print(f"📊 Frame {frame_count}: Success={success_count}, Errors={error_count}, FPS={fps_counter.fps():.1f}")
                    if len(blobs) > 0:
                        print(f"   Found {len(blobs)} blobs at native resolution")
                
            except Exception as e:
                error_count += 1
                print(f"⚠️ Frame {frame_count} error: {e}")
                
                # 尝试重新初始化摄像头
                if error_count % 5 == 0:
                    print("🔄 Attempting camera restart...")
                    try:
                        sensor.stop()
                        time.sleep_ms(200)
                        sensor.run()
                        print("✅ Camera restarted")
                    except Exception as restart_error:
                        print(f"❌ Camera restart failed: {restart_error}")
                
                time.sleep_ms(200)
                continue

    except KeyboardInterrupt:
        print("\n🛑 Test stopped by user")
    except Exception as e:
        print(f"❌ System error: {e}")
        import sys
        sys.print_exception(e)
    finally:
        # 清理资源 - 基于您的工作参考代码
        print("🧹 Cleaning up...")
        try:
            if sensor:
                sensor.stop()
                print("✅ Camera stopped")
            Display.deinit()
            print("✅ Display stopped")
            os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
            time.sleep_ms(100)
            MediaManager.deinit()
            print("✅ Media manager stopped")
        except Exception as e:
            print(f"⚠️ Cleanup error: {e}")
        print("✅ Test completed")
        
        # 显示最终统计
        if 'success_count' in locals() and 'error_count' in locals():
            total = success_count + error_count
            if total > 0:
                success_rate = (success_count / total) * 100
                print(f"📊 Final Stats: Success={success_count}, Errors={error_count}, Rate={success_rate:.1f}%")

if __name__ == "__main__":
    native_resolution_test()
