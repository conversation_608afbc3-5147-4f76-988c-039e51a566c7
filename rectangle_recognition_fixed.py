# K230 Rectangle Frame Recognition System - Fixed Version
# Copyright: Mi Cu Electronics Studio
# Developer: <PERSON> (<PERSON> Engineer)

import time, os, sys, math
from media.sensor import *
from media.display import *
from media.media import *

# System configuration - Optimized for K230
PICTURE_WIDTH = 320
PICTURE_HEIGHT = 240
SENSOR_ID = 2
DISPLAY_MODE = "LCD"

# Display settings
if DISPLAY_MODE == "VIRT":
    DISPLAY_WIDTH = ALIGN_UP(1920, 16)
    DISPLAY_HEIGHT = 1080
elif DISPLAY_MODE == "LCD":
    DISPLAY_WIDTH = 800
    DISPLAY_HEIGHT = 480
elif DISPLAY_MODE == "HDMI":
    DISPLAY_WIDTH = 1920
    DISPLAY_HEIGHT = 1080
else:
    raise ValueError("Unknown DISPLAY_MODE")

# Rectangle Frame Analyzer Class
class RectFrameAnalyzer:
    def __init__(self, debug=True):
        self.debug = debug
        self.outer_rect = None
        self.inner_rect = None
        self.shape_type = "Unknown"
        self.distances = {"left": 0, "right": 0, "top": 0, "bottom": 0}
        
    def detect_frames(self, img):
        """Detect rectangle frames"""
        # Convert to grayscale and binary
        img_gray = img.to_grayscale(copy=True)
        img_binary = img_gray.binary([(0, 80)])
        
        # Find rectangles
        rects = img_binary.find_rects(threshold=3000)
        
        if len(rects) >= 2:
            # Sort by area - larger is outer, smaller is inner
            rects_sorted = sorted(rects, key=lambda r: r.w() * r.h(), reverse=True)
            self.outer_rect = rects_sorted[0]
            self.inner_rect = rects_sorted[1]
            
            if self.debug:
                print("Outer frame:", self.outer_rect.rect())
                print("Inner frame:", self.inner_rect.rect())
            return True
        elif len(rects) == 1:
            self.outer_rect = rects[0]
            self.inner_rect = None
            if self.debug:
                print("Single rect:", self.outer_rect.rect())
            return False
        else:
            if self.debug:
                print("No rectangles found")
            return False
    
    def calc_distances(self):
        """Calculate frame distances"""
        if self.outer_rect is None or self.inner_rect is None:
            return self.distances
            
        # Get coordinates
        ox, oy, ow, oh = self.outer_rect.rect()
        ix, iy, iw, ih = self.inner_rect.rect()
        
        # Calculate distances
        self.distances["left"] = abs(ix - ox)
        self.distances["right"] = abs((ox + ow) - (ix + iw))
        self.distances["top"] = abs(iy - oy)
        self.distances["bottom"] = abs((oy + oh) - (iy + ih))
        
        if self.debug:
            print("Distances L:", self.distances["left"], 
                  "R:", self.distances["right"],
                  "T:", self.distances["top"], 
                  "B:", self.distances["bottom"])
        
        return self.distances
    
    def detect_shape(self, img):
        """Detect internal shape"""
        if self.inner_rect is None:
            return "No_Area"
            
        # Get ROI
        ix, iy, iw, ih = self.inner_rect.rect()
        roi = (ix + 5, iy + 5, iw - 10, ih - 10)
        
        # Detect shapes
        circles = img.find_circles(roi=roi, threshold=1000)
        inner_rects = img.find_rects(roi=roi, threshold=1000)
        lines = img.find_line_segments(roi=roi, merge_distance=10, max_theta_diff=15)
        
        # Simple classification
        if len(circles) > 0:
            self.shape_type = "Circle"
        elif len(inner_rects) > 0:
            self.shape_type = "Rectangle"
        elif len(lines) == 3:
            self.shape_type = "Triangle"
        elif len(lines) == 4:
            self.shape_type = "Rectangle"
        elif len(lines) > 4:
            self.shape_type = "Circle"
        else:
            self.shape_type = "Unknown"
            
        if self.debug:
            print("Shape detected:", self.shape_type)
            
        return self.shape_type
    
    def draw_results(self, img):
        """Draw detection results"""
        # Draw outer frame (red)
        if self.outer_rect:
            img.draw_rectangle(self.outer_rect.rect(), color=(255, 0, 0), thickness=3)
            img.draw_string_advanced(self.outer_rect.x(), self.outer_rect.y() - 25, 16, 
                                   "Outer", color=(255, 0, 0))
        
        # Draw inner frame (green)
        if self.inner_rect:
            img.draw_rectangle(self.inner_rect.rect(), color=(0, 255, 0), thickness=2)
            img.draw_string_advanced(self.inner_rect.x(), self.inner_rect.y() - 25, 16, 
                                   "Inner", color=(0, 255, 0))
            
            # Draw shape type
            cx = self.inner_rect.x() + self.inner_rect.w() // 2
            cy = self.inner_rect.y() + self.inner_rect.h() // 2
            img.draw_string_advanced(cx - 30, cy, 20, self.shape_type, color=(0, 0, 255))
            
            # Draw distances
            self.draw_distances(img)
        
        # Draw info panel
        self.draw_info(img)
    
    def draw_distances(self, img):
        """Draw distance measurements"""
        if self.outer_rect is None or self.inner_rect is None:
            return
            
        ox, oy, ow, oh = self.outer_rect.rect()
        ix, iy, iw, ih = self.inner_rect.rect()
        
        # Left distance
        if self.distances["left"] > 3:
            mid_y = oy + oh // 2
            img.draw_line(ox, mid_y, ix, mid_y, color=(255, 255, 0), thickness=2)
            img.draw_string_advanced(ox + 5, mid_y - 10, 12, 
                                   str(self.distances["left"]), color=(255, 255, 0))
        
        # Right distance
        if self.distances["right"] > 3:
            mid_y = oy + oh // 2
            start_x = ix + iw
            end_x = ox + ow
            img.draw_line(start_x, mid_y, end_x, mid_y, color=(255, 255, 0), thickness=2)
            img.draw_string_advanced(start_x + 5, mid_y - 10, 12, 
                                   str(self.distances["right"]), color=(255, 255, 0))
        
        # Top distance
        if self.distances["top"] > 3:
            mid_x = ox + ow // 2
            img.draw_line(mid_x, oy, mid_x, iy, color=(255, 255, 0), thickness=2)
            img.draw_string_advanced(mid_x + 5, oy + 5, 12, 
                                   str(self.distances["top"]), color=(255, 255, 0))
        
        # Bottom distance
        if self.distances["bottom"] > 3:
            mid_x = ox + ow // 2
            start_y = iy + ih
            end_y = oy + oh
            img.draw_line(mid_x, start_y, mid_x, end_y, color=(255, 255, 0), thickness=2)
            img.draw_string_advanced(mid_x + 5, start_y + 5, 12, 
                                   str(self.distances["bottom"]), color=(255, 255, 0))
    
    def draw_info(self, img):
        """Draw info panel"""
        info_lines = [
            "K230 Rect System",
            "Outer: " + ("OK" if self.outer_rect else "NO"),
            "Inner: " + ("OK" if self.inner_rect else "NO"),
            "Shape: " + self.shape_type,
        ]
        
        for i, line in enumerate(info_lines):
            img.draw_string_advanced(10, 10 + i * 20, 14, line, color=(255, 255, 255))

# Main function
def main():
    sensor = None
    analyzer = RectFrameAnalyzer(debug=True)
    
    try:
        # Initialize camera
        sensor = Sensor(id=SENSOR_ID)
        sensor.reset()
        sensor.set_framesize(width=PICTURE_WIDTH, height=PICTURE_HEIGHT, chn=CAM_CHN_ID_0)
        sensor.set_pixformat(Sensor.RGB565, chn=CAM_CHN_ID_0)
        
        # Initialize display
        if DISPLAY_MODE == "VIRT":
            Display.init(Display.VIRT, width=DISPLAY_WIDTH, height=DISPLAY_HEIGHT, fps=60)
        elif DISPLAY_MODE == "LCD":
            Display.init(Display.ST7701, width=DISPLAY_WIDTH, height=DISPLAY_HEIGHT, to_ide=True)
        elif DISPLAY_MODE == "HDMI":
            Display.init(Display.LT9611, width=DISPLAY_WIDTH, height=DISPLAY_HEIGHT, to_ide=True)
        
        # Initialize media manager
        MediaManager.init()
        sensor.run()
        
        print("=== K230 Rectangle Recognition System Started ===")
        
        while True:
            os.exitpoint()
            
            # Get image
            img = sensor.snapshot(chn=CAM_CHN_ID_0)
            
            # Process
            if analyzer.detect_frames(img):
                analyzer.calc_distances()
                analyzer.detect_shape(img)
            
            # Draw results
            analyzer.draw_results(img)
            
            # Display
            Display.show_image(img, x=int((DISPLAY_WIDTH - PICTURE_WIDTH) / 2), 
                             y=int((DISPLAY_HEIGHT - PICTURE_HEIGHT) / 2))
            
            time.sleep_ms(50)
            
    except KeyboardInterrupt as e:
        print("User stopped:", e)
    except BaseException as e:
        print("Exception:", e)
    finally:
        # Cleanup
        if isinstance(sensor, Sensor):
            sensor.stop()
        Display.deinit()
        os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
        time.sleep_ms(100)
        MediaManager.deinit()
        print("System shutdown complete")

if __name__ == "__main__":
    main()
