# K230矩形边框识别与内部图形分类系统 PRD

## 📋 文档信息
- **版本**: v1.0
- **创建时间**: 2025-07-30
- **负责人**: Emma (产品经理)
- **版权归属**: 米醋电子工作室
- **项目代号**: K230-ShapeRecognition

## 🎯 背景与问题陈述

### 问题描述
老板需要一个基于K230芯片的智能图形识别系统，能够：
1. 识别空心黑色矩形的内边框和外边框
2. 计算内外边框之间的垂直距离
3. 识别矩形内部的图形类型（三角形、矩形、圆形）

### 应用场景
- 工业质检：检测产品边框厚度和内部结构
- 教育应用：几何图形自动识别和分类
- 机器视觉：复杂图形的结构化分析

## 🎯 目标与成功指标

### 项目目标 (Objectives)
1. **O1**: 实现高精度的矩形边框检测，准确率≥95%
2. **O2**: 精确计算内外边框垂直距离，误差≤2像素
3. **O3**: 准确识别内部图形类型，分类准确率≥90%

### 关键结果 (Key Results)
- **KR1**: 边框检测响应时间≤100ms
- **KR2**: 支持640x640分辨率实时处理
- **KR3**: 系统稳定运行，无内存泄漏
- **KR4**: 可视化显示检测结果和测量数据

### 反向指标 (Counter Metrics)
- 系统资源占用率不超过80%
- 误检率控制在5%以内
- 处理延迟不超过200ms

## 👥 用户画像与用户故事

### 目标用户
- **主要用户**: 工业检测工程师
- **次要用户**: 教育工作者、机器视觉开发者

### 用户故事
1. **作为检测工程师**，我希望能够快速检测产品边框厚度，以便进行质量控制
2. **作为教育工作者**，我希望能够自动识别学生绘制的几何图形，以便进行自动评分
3. **作为开发者**，我希望有清晰的API接口，以便集成到现有系统中

## 🔧 功能规格详述

### 核心功能模块

#### 1. 矩形边框检测模块
**功能描述**: 检测空心黑色矩形的内边框和外边框
**输入**: 640x640 RGB图像
**输出**: 内边框坐标、外边框坐标
**算法**: 基于轮廓检测和层次分析

**技术实现**:
```python
# 边框检测流程
1. 图像预处理 (灰度化、二值化)
2. 轮廓检测 (find_contours)
3. 轮廓筛选 (面积、形状过滤)
4. 层次分析 (区分内外边框)
5. 边框拟合 (矩形拟合优化)
```

#### 2. 距离测量模块
**功能描述**: 计算内外边框之间的垂直距离
**输入**: 内边框坐标、外边框坐标
**输出**: 四条边的垂直距离值
**算法**: 点到直线距离计算

**技术实现**:
```python
# 距离计算方法
1. 提取边框的四条边
2. 计算对应边之间的垂直距离
3. 返回上下左右四个方向的距离值
```

#### 3. 内部图形识别模块
**功能描述**: 识别矩形内部的图形类型
**输入**: 内边框区域图像
**输出**: 图形类型 (三角形/矩形/圆形)
**算法**: 基于轮廓逼近的形状分类

**技术实现**:
```python
# 图形分类逻辑
1. 提取内部区域ROI
2. 轮廓检测和逼近
3. 顶点数量统计
4. 形状分类:
   - 3个顶点 → 三角形
   - 4个顶点 → 矩形
   - >4个顶点 → 圆形
```

#### 4. 可视化显示模块
**功能描述**: 实时显示检测结果
**输入**: 原始图像、检测结果
**输出**: 标注后的显示图像
**功能**: 绘制边框、距离标注、图形分类结果

## 📏 范围定义

### 包含功能 (In Scope)
✅ 空心黑色矩形的边框检测
✅ 内外边框垂直距离计算
✅ 内部图形类型识别 (三角形、矩形、圆形)
✅ 实时图像处理和显示
✅ 检测结果可视化标注
✅ 基本的图像预处理功能

### 排除功能 (Out of Scope)
❌ 彩色矩形的识别
❌ 复杂多边形的精确识别
❌ 图形的精确尺寸测量
❌ 数据存储和历史记录
❌ 网络通信功能
❌ 多目标同时检测

## 🔗 依赖与风险

### 内部依赖
- K230硬件平台和摄像头模块
- CanMV开发环境和图像处理库
- 显示屏幕 (LCD或HDMI)

### 外部依赖
- 良好的光照条件
- 清晰的图像输入
- 稳定的电源供应

### 潜在风险
- **技术风险**: 复杂背景下的边框检测准确性
- **性能风险**: 实时处理的计算资源限制
- **环境风险**: 光照变化对检测效果的影响

### 风险缓解策略
- 实现自适应阈值算法
- 优化算法性能，减少计算复杂度
- 增加图像预处理步骤，提高鲁棒性

## 🚀 发布初步计划

### 开发阶段
1. **算法原型开发** (1天)
2. **K230平台适配** (1天)  
3. **功能测试验证** (0.5天)
4. **性能优化调试** (0.5天)

### 测试策略
- 单元测试: 各模块功能验证
- 集成测试: 完整流程测试
- 性能测试: 实时性和准确性验证
- 边界测试: 极端条件下的稳定性

### 验收标准
- 边框检测准确率≥95%
- 距离测量误差≤2像素
- 图形分类准确率≥90%
- 系统响应时间≤100ms
- 连续运行24小时无异常

---

**Emma完成PRD编写，等待Mike指令进入下一阶段开发。**
