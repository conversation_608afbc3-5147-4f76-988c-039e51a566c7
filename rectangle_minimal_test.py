# K230矩形识别系统 - 最小化测试版本
# 专门解决 sensor snapshot failed 问题
# 完全基于您的工作参考代码架构

import time
import os
import sys
import gc

from media.sensor import *
from media.display import *
from media.media import *

# 显示配置参数 - 完全基于您的工作参考代码
DETECT_WIDTH = ALIGN_UP(640, 16)
DETECT_HEIGHT = 480

def minimal_rectangle_test():
    """最小化矩形检测测试"""
    print("🎯 K230 Minimal Rectangle Test")
    print("📅 Based on Your Working Reference Code")
    print("🔧 Solving sensor snapshot failed issue")
    
    sensor = None
    
    try:
        # 启用退出点 - 基于您的工作参考代码
        os.exitpoint(os.EXITPOINT_ENABLE)
        
        print("🔧 Step 1: Initializing camera...")
        
        # 初始化摄像头 - 完全基于您的工作参考代码
        sensor = Sensor(width=DETECT_WIDTH, height=DETECT_HEIGHT)
        sensor.reset()
        sensor.set_framesize(width=DETECT_WIDTH, height=DETECT_HEIGHT)
        sensor.set_pixformat(Sensor.RGB565)
        print(f"✅ Camera configured: {DETECT_WIDTH}x{DETECT_HEIGHT}")

        print("🖥️ Step 2: Initializing LCD display...")
        
        # 初始化显示 - 完全基于您的工作参考代码
        Display.init(Display.ST7701, to_ide=True)
        print("✅ LCD Display initialized (ST7701)")

        print("📱 Step 3: Initializing media manager...")
        
        # 初始化媒体管理器 - 基于您的工作参考代码
        MediaManager.init()
        print("✅ Media manager initialized")
        
        print("🚀 Step 4: Starting camera...")
        
        # 启动摄像头 - 基于您的工作参考代码
        sensor.run()
        print("✅ Camera started")

        print("🎯 System Ready - Testing snapshot...")
        print("⌨️ Press Ctrl+C to stop")
        
        # FPS计数器
        fps_counter = time.clock()
        frame_count = 0
        success_count = 0
        error_count = 0

        while True:
            fps_counter.tick()
            os.exitpoint()
            frame_count += 1

            try:
                # 测试摄像头快照获取 - 基于您的工作参考代码
                img = sensor.snapshot()
                success_count += 1
                
                # 简单的矩形检测测试
                black_threshold = [(0, 30, -128, 127, -128, 127)]
                blobs = img.find_blobs(black_threshold, pixels_threshold=50, area_threshold=50, merge=True)
                
                # 绘制检测结果
                for blob in blobs:
                    img.draw_rectangle(blob.x(), blob.y(), blob.w(), blob.h(),
                                     color=(255, 0, 0), thickness=3, fill=False)
                
                # 绘制测试信息
                img.draw_string_advanced(10, 10, 20, "Snapshot Test OK", color=(0, 255, 0))
                img.draw_string_advanced(10, 35, 16, f"Frame: {frame_count}", color=(255, 255, 0))
                img.draw_string_advanced(10, 55, 16, f"Success: {success_count}", color=(0, 255, 255))
                img.draw_string_advanced(10, 75, 16, f"Errors: {error_count}", color=(255, 0, 0))
                img.draw_string_advanced(10, 95, 16, f"Blobs: {len(blobs)}", color=(255, 255, 255))
                
                # 显示FPS
                fps_text = f"FPS: {fps_counter.fps():.1f}"
                img.draw_string_advanced(DETECT_WIDTH-120, 20, 16, fps_text, color=(255, 255, 0))
                
                # 显示图像到LCD屏幕 - 基于您的工作参考代码
                Display.show_image(img)
                
                # 内存管理 - 基于您的工作参考代码
                gc.collect()
                
                # 每50帧打印一次状态
                if frame_count % 50 == 0:
                    print(f"📊 Frame {frame_count}: Success={success_count}, Errors={error_count}, FPS={fps_counter.fps():.1f}")
                    if len(blobs) > 0:
                        print(f"   Found {len(blobs)} blobs")
                
            except Exception as e:
                error_count += 1
                print(f"⚠️ Frame {frame_count} error: {e}")
                
                # 尝试重新初始化摄像头
                if error_count % 10 == 0:
                    print("🔄 Attempting camera restart...")
                    try:
                        sensor.stop()
                        time.sleep_ms(100)
                        sensor.run()
                        print("✅ Camera restarted")
                    except Exception as restart_error:
                        print(f"❌ Camera restart failed: {restart_error}")
                
                time.sleep_ms(100)
                continue

    except KeyboardInterrupt:
        print("\n🛑 Test stopped by user")
    except Exception as e:
        print(f"❌ System error: {e}")
        import sys
        sys.print_exception(e)
    finally:
        # 清理资源 - 基于您的工作参考代码
        print("🧹 Cleaning up...")
        try:
            if sensor:
                sensor.stop()
                print("✅ Camera stopped")
            Display.deinit()
            print("✅ Display stopped")
            os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
            time.sleep_ms(100)
            MediaManager.deinit()
            print("✅ Media manager stopped")
        except Exception as e:
            print(f"⚠️ Cleanup error: {e}")
        print("✅ Test completed")
        
        # 显示最终统计
        if 'success_count' in locals() and 'error_count' in locals():
            total = success_count + error_count
            if total > 0:
                success_rate = (success_count / total) * 100
                print(f"📊 Final Stats: Success={success_count}, Errors={error_count}, Rate={success_rate:.1f}%")

if __name__ == "__main__":
    minimal_rectangle_test()
