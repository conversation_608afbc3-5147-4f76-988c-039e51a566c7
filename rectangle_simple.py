# K230 Rectangle Recognition - Ultra Simple Version
# Memory optimized for K230 platform

import time, os, sys
from media.sensor import *
from media.display import *
from media.media import *

# Ultra-light configuration
PICTURE_WIDTH = 240
PICTURE_HEIGHT = 180
SENSOR_ID = 2
DISPLAY_MODE = "LCD"

# Display settings
if DISPLAY_MODE == "LCD":
    DISPLAY_WIDTH = 800
    DISPLAY_HEIGHT = 480
else:
    DISPLAY_WIDTH = 1920
    DISPLAY_HEIGHT = 1080

class SimpleRectDetector:
    def __init__(self):
        self.outer_rect = None
        self.inner_rect = None
        self.shape_type = "Unknown"
        self.distances = [0, 0, 0, 0]  # left, right, top, bottom
        
    def detect_rects(self, img):
        """Simple rectangle detection"""
        try:
            # Convert to grayscale (no copy to save memory)
            img.to_grayscale(copy=False)
            
            # Simple binary threshold
            img.binary([(0, 100)])
            
            # Find rectangles with low threshold
            rects = img.find_rects(threshold=1000)
            
            if len(rects) >= 2:
                # Sort by area
                rects.sort(key=lambda r: r.w() * r.h(), reverse=True)
                self.outer_rect = rects[0]
                self.inner_rect = rects[1]
                print("Found 2 rectangles")
                return True
            elif len(rects) == 1:
                self.outer_rect = rects[0]
                self.inner_rect = None
                print("Found 1 rectangle")
                return False
            else:
                print("No rectangles")
                return False
                
        except Exception as e:
            print("Detection error:", e)
            return False
    
    def calc_distances(self):
        """Calculate simple distances"""
        if not self.outer_rect or not self.inner_rect:
            return
            
        ox, oy, ow, oh = self.outer_rect.rect()
        ix, iy, iw, ih = self.inner_rect.rect()
        
        self.distances[0] = ix - ox  # left
        self.distances[1] = (ox + ow) - (ix + iw)  # right
        self.distances[2] = iy - oy  # top
        self.distances[3] = (oy + oh) - (iy + ih)  # bottom
        
        print("Distances:", self.distances)
    
    def detect_shape(self, img):
        """Simple shape detection"""
        if not self.inner_rect:
            self.shape_type = "None"
            return
            
        try:
            ix, iy, iw, ih = self.inner_rect.rect()
            roi = (ix + 3, iy + 3, iw - 6, ih - 6)
            
            # Try circle detection
            circles = img.find_circles(roi=roi, threshold=2000)
            if len(circles) > 0:
                self.shape_type = "Circle"
                return
            
            # Try rectangle detection
            rects = img.find_rects(roi=roi, threshold=1000)
            if len(rects) > 0:
                self.shape_type = "Rectangle"
                return
                
            # Try line detection
            lines = img.find_line_segments(roi=roi)
            if len(lines) == 3:
                self.shape_type = "Triangle"
            elif len(lines) >= 4:
                self.shape_type = "Polygon"
            else:
                self.shape_type = "Unknown"
                
        except Exception as e:
            print("Shape error:", e)
            self.shape_type = "Error"
    
    def draw_simple(self, img):
        """Simple drawing"""
        try:
            # Draw outer rect (red)
            if self.outer_rect:
                img.draw_rectangle(self.outer_rect.rect(), color=(255, 0, 0), thickness=2)
            
            # Draw inner rect (green)
            if self.inner_rect:
                img.draw_rectangle(self.inner_rect.rect(), color=(0, 255, 0), thickness=1)
                
                # Draw shape type
                cx = self.inner_rect.x() + self.inner_rect.w() // 2
                cy = self.inner_rect.y() + self.inner_rect.h() // 2
                img.draw_string_advanced(cx - 20, cy, 16, self.shape_type, color=(0, 0, 255))
            
            # Draw simple info
            img.draw_string_advanced(5, 5, 12, "K230 Simple Rect", color=(255, 255, 255))
            if self.outer_rect and self.inner_rect:
                info = "Outer+Inner OK"
            elif self.outer_rect:
                info = "Outer Only"
            else:
                info = "No Rect"
            img.draw_string_advanced(5, 20, 12, info, color=(255, 255, 255))
            
        except Exception as e:
            print("Draw error:", e)

def main():
    sensor = None
    detector = SimpleRectDetector()
    
    try:
        # Initialize camera with minimal settings
        sensor = Sensor(id=SENSOR_ID)
        sensor.reset()
        sensor.set_framesize(width=PICTURE_WIDTH, height=PICTURE_HEIGHT, chn=CAM_CHN_ID_0)
        sensor.set_pixformat(Sensor.RGB565, chn=CAM_CHN_ID_0)
        
        # Initialize display
        Display.init(Display.ST7701, width=DISPLAY_WIDTH, height=DISPLAY_HEIGHT, to_ide=True)
        
        # Initialize media
        MediaManager.init()
        sensor.run()
        
        print("=== K230 Simple Rectangle System ===")
        
        frame_count = 0
        while True:
            os.exitpoint()
            
            try:
                # Get image
                img = sensor.snapshot(chn=CAM_CHN_ID_0)
                
                # Process every 3rd frame to save memory
                if frame_count % 3 == 0:
                    if detector.detect_rects(img):
                        detector.calc_distances()
                        detector.detect_shape(img)
                
                # Always draw (lightweight)
                detector.draw_simple(img)
                
                # Display
                Display.show_image(img, x=int((DISPLAY_WIDTH - PICTURE_WIDTH) / 2), 
                                 y=int((DISPLAY_HEIGHT - PICTURE_HEIGHT) / 2))
                
                frame_count += 1
                time.sleep_ms(100)  # Slower processing to save memory
                
            except Exception as e:
                print("Frame error:", e)
                time.sleep_ms(200)
                continue
            
    except KeyboardInterrupt:
        print("User stopped")
    except Exception as e:
        print("System error:", e)
    finally:
        # Cleanup
        try:
            if sensor:
                sensor.stop()
            Display.deinit()
            MediaManager.deinit()
        except:
            pass
        print("System stopped")

if __name__ == "__main__":
    main()
