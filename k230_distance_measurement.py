# K230 Distance Measurement System - A4纸标记距离测量
# Copyright: Mi Cu Electronics Studio
# Developer: <PERSON> (Mike Team Engineer)
# 完全兼容K230的距离测量系统，替代OpenCV版本

import time
import os
import sys
import math
import image

# K230专用导入
try:
    from media.sensor import Sensor
    from media.display import Display
    from media.media import MediaManager
    print("✓ K230 media modules imported successfully")
except ImportError as e:
    print(f"❌ K230 import error: {e}")
    sys.exit(1)

# K230距离测量系统配置
DISPLAY_WIDTH = 800
DISPLAY_HEIGHT = 480
PICTURE_WIDTH = 640
PICTURE_HEIGHT = 480

# 距离测量参数 - 基于A4纸标记 (用户已校准)
KNOWN_WIDTH = 10.31  # A4纸的实际宽度 (inch) - 用户校准值
FOCAL_LENGTH = 551.8902  # 焦距 (pixels) - 用户校准值

# 全局变量
sensor = None
current_distance = 0.0
marker_found = False

def distance_to_camera(knownWidth, focalLength, perWidth):
    """根据标记的像素宽度估计距离"""
    if perWidth <= 0:
        return 0
    return (knownWidth * focalLength) / perWidth

def find_marker(img):
    """K230版本：检测A4纸标记并计算距离"""
    global marker_found, current_distance
    
    try:
        print("=== A4纸标记检测开始 ===")
        
        # 使用边缘检测方法：检测黑色边框
        # 在K230中，我们使用find_blobs来模拟轮廓检测
        black_threshold = [(0, 50, -128, 127, -128, 127)]  # LAB颜色空间的黑色
        
        # 设置搜索区域，避免边缘噪声
        margin = 30
        search_roi = (margin, margin, PICTURE_WIDTH - 2*margin, PICTURE_HEIGHT - 2*margin)
        
        # 检测黑色区域（模拟轮廓检测）
        blobs = img.find_blobs(black_threshold, False, search_roi,
                              x_stride=2, y_stride=2,
                              pixels_threshold=500, margin=True)
        
        if len(blobs) == 0:
            marker_found = False
            print("❌ 未检测到A4纸标记")
            return None
            
        print(f"检测到 {len(blobs)} 个候选标记")
        
        # 筛选最可能的A4纸标记
        valid_markers = []
        for blob in blobs:
            bw, bh = blob.w(), blob.h()
            area = blob.pixels()
            bbox_area = bw * bh
            fill_ratio = area / bbox_area if bbox_area > 0 else 0
            aspect_ratio = max(bw, bh) / min(bw, bh) if min(bw, bh) > 0 else 1.0
            
            # A4纸标记的特征判断
            is_a4_marker = (
                # 1. 尺寸合理：不能太小也不能太大
                50 < bw < PICTURE_WIDTH * 0.8 and
                50 < bh < PICTURE_HEIGHT * 0.8 and
                # 2. 面积合理
                2000 < area < PICTURE_WIDTH * PICTURE_HEIGHT * 0.6 and
                # 3. 填充比例：A4纸边框应该是空心的
                0.1 < fill_ratio < 0.6 and
                # 4. 长宽比：A4纸的长宽比约为1.414 (√2)
                1.2 < aspect_ratio < 1.8
            )
            
            if is_a4_marker:
                valid_markers.append((blob, aspect_ratio, area))
                print(f"  ✓ 有效A4标记: {bw}x{bh}, 长宽比={aspect_ratio:.2f}, 面积={area}")
        
        if len(valid_markers) == 0:
            marker_found = False
            print("❌ 未找到符合A4纸特征的标记")
            return None
            
        # 选择最佳标记（最接近A4纸长宽比的）
        best_marker = min(valid_markers, key=lambda x: abs(x[1] - 1.414))[0]
        
        # 创建标记矩形对象（模拟OpenCV的minAreaRect）
        class MarkerRect:
            def __init__(self, blob):
                self.blob = blob
                self._center = (blob.cx(), blob.cy())
                self._size = (blob.w(), blob.h())
                self._angle = 0  # K230的blob没有角度信息，假设为0
                
            def center(self):
                return self._center
                
            def size(self):
                return self._size
                
            def angle(self):
                return self._angle
        
        marker_found = True
        marker_rect = MarkerRect(best_marker)
        
        print(f"✓ 检测到A4纸标记: 中心=({marker_rect.center()[0]}, {marker_rect.center()[1]}), 尺寸={marker_rect.size()}")
        
        return marker_rect
        
    except Exception as e:
        print(f"标记检测错误: {e}")
        marker_found = False
        return None

def calculate_marker_distance(marker_rect):
    """计算A4纸标记的距离"""
    global current_distance
    
    if not marker_rect:
        current_distance = 0.0
        return 0.0
    
    try:
        # 获取标记的像素宽度
        marker_width_pixels = marker_rect.size()[0]  # 使用宽度进行距离计算
        
        # 计算距离（英寸）
        distance_inches = distance_to_camera(KNOWN_WIDTH, FOCAL_LENGTH, marker_width_pixels)
        
        # 转换为厘米（1英寸 = 2.54厘米）
        distance_cm = distance_inches * 2.54
        
        current_distance = distance_cm
        
        print(f"距离计算: 像素宽度={marker_width_pixels}, 距离={distance_cm:.1f}cm ({distance_inches:.1f}inch)")
        
        return distance_cm
        
    except Exception as e:
        print(f"距离计算错误: {e}")
        current_distance = 0.0
        return 0.0

def draw_marker_and_distance(img, marker_rect, distance):
    """在图像上绘制标记和距离信息"""
    if not marker_rect:
        return
        
    try:
        # 获取标记信息
        center = marker_rect.center()
        size = marker_rect.size()
        
        # 计算矩形的四个角点（简化版本，假设没有旋转）
        half_w = size[0] // 2
        half_h = size[1] // 2
        
        # 绘制标记边框（绿色）
        rect_x = center[0] - half_w
        rect_y = center[1] - half_h
        img.draw_rectangle(rect_x, rect_y, size[0], size[1], 
                          color=(0, 255, 0), thickness=3, fill=False)
        
        # 绘制中心点
        img.draw_circle(center[0], center[1], 5, color=(255, 0, 0), thickness=2, fill=True)
        
        # 显示距离信息
        if distance > 0:
            distance_text = f"Distance: {distance:.1f}cm"
            img.draw_string_advanced(PICTURE_WIDTH - 200, PICTURE_HEIGHT - 30, 20, 
                                   distance_text, color=(0, 255, 0))
            
            # 显示英寸距离
            distance_inches = distance / 2.54
            inch_text = f"({distance_inches:.1f} inch)"
            img.draw_string_advanced(PICTURE_WIDTH - 200, PICTURE_HEIGHT - 10, 16, 
                                   inch_text, color=(150, 150, 150))
        
        # 显示标记尺寸
        size_text = f"Marker: {size[0]}x{size[1]} px"
        img.draw_string_advanced(10, PICTURE_HEIGHT - 50, 16, size_text, color=(255, 255, 0))
        
        # 显示中心坐标
        center_text = f"Center: ({center[0]}, {center[1]})"
        img.draw_string_advanced(10, PICTURE_HEIGHT - 30, 16, center_text, color=(255, 255, 0))
        
    except Exception as e:
        print(f"绘制错误: {e}")

def main():
    """K230距离测量系统主函数"""
    global sensor

    try:
        print("K230 Distance Measurement System Starting...")

        # 启用退出点
        os.exitpoint(os.EXITPOINT_ENABLE)

        # 初始化摄像头
        sensor = Sensor(width=PICTURE_WIDTH, height=PICTURE_HEIGHT)
        sensor.reset()
        sensor.set_framesize(width=PICTURE_WIDTH, height=PICTURE_HEIGHT)
        sensor.set_pixformat(Sensor.RGB565)
        print(f"Camera configured: {PICTURE_WIDTH}x{PICTURE_HEIGHT}")

        # 初始化LCD显示
        Display.init(Display.ST7701, to_ide=True)
        print("K230 LCD screen initialized (ST7701)")

        # 初始化媒体管理器
        MediaManager.init()
        print("Media manager initialized")

        # 启动摄像头
        sensor.run()
        print("Camera started")

        print("=== K230 Distance Measurement Ready ===")
        print(f"已知A4纸宽度: {KNOWN_WIDTH} inch ({KNOWN_WIDTH * 2.54:.1f} cm)")
        print(f"相机焦距: {FOCAL_LENGTH} pixels")

        frame_count = 0

        while True:
            os.exitpoint()

            try:
                # 获取图像
                img = sensor.snapshot()

                # 每5帧检测一次，提高响应性
                frame_count += 1
                if frame_count % 5 == 0:
                    # 检测A4纸标记
                    marker = find_marker(img)
                    
                    if marker:
                        # 计算距离
                        distance = calculate_marker_distance(marker)
                        
                        # 绘制标记和距离信息
                        draw_marker_and_distance(img, marker, distance)
                        
                        print(f"检测成功: 距离 = {distance:.1f}cm")
                    else:
                        # 显示搜索状态
                        img.draw_string_advanced(10, 10, 20, "SEARCHING A4 MARKER...", color=(255, 255, 0))
                        img.draw_string_advanced(10, 35, 16, "Place A4 paper in view", color=(150, 150, 150))
                else:
                    # 非检测帧，只显示上次的结果
                    if marker_found and current_distance > 0:
                        # 显示缓存的距离信息
                        distance_text = f"Distance: {current_distance:.1f}cm"
                        img.draw_string_advanced(PICTURE_WIDTH - 200, PICTURE_HEIGHT - 30, 20, 
                                               distance_text, color=(0, 255, 0))
                        
                        distance_inches = current_distance / 2.54
                        inch_text = f"({distance_inches:.1f} inch)"
                        img.draw_string_advanced(PICTURE_WIDTH - 200, PICTURE_HEIGHT - 10, 16, 
                                               inch_text, color=(150, 150, 150))

                # 显示系统信息
                img.draw_string_advanced(10, PICTURE_HEIGHT - 10, 14, 
                                       "K230 Distance Measurement v1.0", color=(0, 255, 255))

                # 显示帧率信息
                if frame_count % 30 == 0:  # 每30帧更新一次帧率显示
                    current_time = time.time()
                    if hasattr(main, 'last_time'):
                        fps = 30.0 / (current_time - main.last_time) if current_time != main.last_time else 0
                        img.draw_string_advanced(PICTURE_WIDTH - 80, 10, 14, f"FPS:{fps:.1f}", color=(150, 150, 150))
                    main.last_time = current_time

                # 显示图像
                Display.show_image(img)

                # 控制帧率，平衡流畅性和处理性能
                time.sleep_ms(50)  # 约20FPS

            except Exception as e:
                print("Processing error:", str(e))
                # 显示错误信息
                img.draw_string_advanced(10, 10, 18, "ERROR OCCURRED", color=(255, 0, 0))
                img.draw_string_advanced(10, 35, 14, str(e)[:40], color=(255, 0, 0))
                Display.show_image(img)
                time.sleep_ms(1000)
                continue

    except KeyboardInterrupt as e:
        print("User stopped:", e)
    except BaseException as e:
        print("System error:", e)
    finally:
        # 清理资源
        try:
            if isinstance(sensor, Sensor):
                sensor.stop()
            Display.deinit()
            os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
            time.sleep_ms(100)
            MediaManager.deinit()
        except:
            pass
        print("System cleanup completed")

if __name__ == "__main__":
    main()
