# A4纸距离测量系统 - 使用指南与校准手册

## 📋 文档信息
- **版本**: v1.0
- **创建时间**: 2025-07-30
- **负责人**: Emma (产品经理)
- **版权归属**: 米醋电子工作室
- **适用设备**: K230开发板

## 🎯 A4纸特征标记详解

### 什么是A4纸特征标记？

A4纸特征标记是指**A4纸的黑色边框轮廓**，系统通过识别这个边框来计算距离。

#### 标准A4纸规格
- **国际标准尺寸**: 210mm × 297mm (8.27" × 11.69")
- **长宽比**: √2 ≈ 1.414 (这是关键识别特征)
- **系统使用宽度**: 210mm = 8.27英寸 (用于距离计算)

### 🔍 系统如何识别A4纸？

#### 1. **视觉特征检测**
```python
# 系统检测的A4纸特征
is_a4_marker = (
    # 1. 尺寸合理：不能太小也不能太大
    50 < bw < PICTURE_WIDTH * 0.8 and
    50 < bh < PICTURE_HEIGHT * 0.8 and
    # 2. 面积合理
    2000 < area < PICTURE_WIDTH * PICTURE_HEIGHT * 0.6 and
    # 3. 填充比例：A4纸边框应该是空心的
    0.1 < fill_ratio < 0.6 and
    # 4. 长宽比：A4纸的长宽比约为1.414 (√2)
    1.2 < aspect_ratio < 1.8  # 允许一定误差范围
)
```

#### 2. **最佳标记制作方法**

**推荐方案A：黑色边框A4纸**
```
┌─────────────────────────────────┐
│ ████████████████████████████████ │ ← 黑色边框 (5-10mm宽)
│ █                              █ │
│ █        白色区域                █ │
│ █                              █ │
│ █                              █ │
│ █                              █ │
│ ████████████████████████████████ │
└─────────────────────────────────┘
```

**推荐方案B：打印边框模板**
- 用打印机在A4纸上打印粗黑边框
- 边框宽度：5-10mm
- 内部保持白色或浅色

**推荐方案C：黑色胶带边框**
- 在A4纸边缘贴黑色胶带
- 形成完整的矩形边框
- 确保边框连续无断点

## ⚙️ 距离校准参数详解

### 核心校准参数

#### 1. **KNOWN_WIDTH (已知宽度)**
```python
KNOWN_WIDTH = 11.0  # A4纸的实际宽度 (inch)
```

**标准值参考**：
- A4纸宽度：210mm = 8.27英寸
- **当前设置偏大**：11.0英寸 ≈ 279mm

**校准建议**：
```python
# 精确校准值
KNOWN_WIDTH = 8.27  # 标准A4纸宽度 (英寸)
# 或者
KNOWN_WIDTH = 8.3   # 稍微放宽的值，考虑测量误差
```

#### 2. **FOCAL_LENGTH (焦距)**
```python
FOCAL_LENGTH = 750  # 焦距 (pixels)
```

**这是最关键的校准参数！**

### 🎯 距离校准步骤

#### 步骤1：准备标准测试环境
1. 制作标准A4纸标记（黑色边框）
2. 准备卷尺或测距仪
3. 选择光线充足、无反光的环境
4. 确保A4纸与摄像头平行

#### 步骤2：焦距校准方法

**方法A：单点校准法**
```python
# 1. 将A4纸放在已知距离处（如50cm）
known_distance_cm = 50.0
known_distance_inch = known_distance_cm / 2.54  # 转换为英寸

# 2. 运行系统，记录检测到的像素宽度
measured_pixel_width = 120  # 从系统输出中获取

# 3. 计算焦距
FOCAL_LENGTH = (KNOWN_WIDTH * measured_pixel_width) / known_distance_inch
print(f"校准后的焦距: {FOCAL_LENGTH}")
```

**方法B：多点校准法（推荐）**
```python
# 在多个距离测试，取平均值
test_distances = [30, 50, 70, 100]  # cm
focal_lengths = []

for distance_cm in test_distances:
    distance_inch = distance_cm / 2.54
    # 记录每个距离的像素宽度
    pixel_width = get_pixel_width_at_distance(distance_cm)
    focal_length = (KNOWN_WIDTH * pixel_width) / distance_inch
    focal_lengths.append(focal_length)

# 取平均值
FOCAL_LENGTH = sum(focal_lengths) / len(focal_lengths)
```

#### 步骤3：验证校准结果
```python
# 在已知距离验证准确性
test_distance = 60  # cm
predicted_distance = distance_to_camera(KNOWN_WIDTH, FOCAL_LENGTH, measured_pixels) * 2.54
error_percentage = abs(predicted_distance - test_distance) / test_distance * 100
print(f"误差: {error_percentage:.1f}%")
```

## 🔧 常见距离不准确问题及解决方案

### 问题1：距离总是偏大
**原因分析**：
- KNOWN_WIDTH设置过大
- FOCAL_LENGTH设置过小

**解决方案**：
```python
# 当前值
KNOWN_WIDTH = 11.0  # 过大
FOCAL_LENGTH = 750

# 修正值
KNOWN_WIDTH = 8.27  # 标准A4宽度
FOCAL_LENGTH = 900  # 增加焦距值
```

### 问题2：距离总是偏小
**原因分析**：
- KNOWN_WIDTH设置过小
- FOCAL_LENGTH设置过大

**解决方案**：
```python
# 修正值
KNOWN_WIDTH = 8.5   # 稍微增加
FOCAL_LENGTH = 650  # 减少焦距值
```

### 问题3：近距离准确，远距离不准
**原因分析**：
- 镜头畸变影响
- 光学焦距非线性

**解决方案**：
```python
# 使用分段校准
def get_focal_length_by_distance(estimated_distance):
    if estimated_distance < 50:
        return 800  # 近距离焦距
    elif estimated_distance < 100:
        return 750  # 中距离焦距
    else:
        return 700  # 远距离焦距
```

### 问题4：检测不稳定
**原因分析**：
- A4纸边框不够清晰
- 光照条件变化
- 角度不平行

**解决方案**：
1. **增强边框对比度**
   ```python
   # 调整检测阈值
   black_threshold = [(0, 40, -128, 127, -128, 127)]  # 更严格的黑色检测
   ```

2. **增加检测稳定性**
   ```python
   # 增加历史平均
   distance_history = []
   current_distance = sum(distance_history[-5:]) / len(distance_history[-5:])
   ```

## 📊 推荐校准参数表

| 使用场景 | KNOWN_WIDTH | FOCAL_LENGTH | 适用距离范围 |
|----------|-------------|--------------|--------------|
| 标准A4纸 | 8.27 | 750-850 | 30-150cm |
| 宽松检测 | 8.5 | 700-800 | 25-200cm |
| 精确测量 | 8.27 | 900-1000 | 40-100cm |
| 远距离 | 8.3 | 600-700 | 100-300cm |

## 🎯 快速校准流程

### 5分钟快速校准
1. **设置标准参数**
   ```python
   KNOWN_WIDTH = 8.27
   FOCAL_LENGTH = 800
   ```

2. **测试50cm距离**
   - 将A4纸放在50cm处
   - 记录系统显示的距离
   - 如果显示60cm，说明参数偏大

3. **调整焦距**
   ```python
   # 如果测量值偏大，增加焦距
   FOCAL_LENGTH = 800 * (60/50) = 960
   
   # 如果测量值偏小，减少焦距
   FOCAL_LENGTH = 800 * (40/50) = 640
   ```

4. **验证其他距离**
   - 测试30cm、70cm、100cm
   - 误差应控制在±5%以内

## 💡 使用技巧

### 最佳使用条件
- **光照**：均匀自然光，避免强光直射
- **背景**：单色背景，避免复杂图案
- **角度**：A4纸与摄像头保持平行
- **距离**：建议测量范围30-150cm

### 提高精度的方法
1. **多次测量取平均值**
2. **保持A4纸平整无弯曲**
3. **确保边框完整清晰**
4. **避免手部遮挡边框**

## 🔍 故障排除检查清单

- [ ] A4纸是否有清晰的黑色边框？
- [ ] 边框是否完整无断点？
- [ ] 光照是否充足均匀？
- [ ] A4纸是否与摄像头平行？
- [ ] KNOWN_WIDTH是否设置为8.27？
- [ ] FOCAL_LENGTH是否在合理范围(700-900)？
- [ ] 测试距离是否在有效范围内？

通过以上指南，您可以获得±3%以内的测量精度！

## 📝 校准参数修改代码示例

### 在K230代码中修改参数

找到以下代码段并修改：

```python
# 在 k230_distance_measurement.py 文件中
# 第25-26行左右

# 距离测量参数 - 基于A4纸标记
KNOWN_WIDTH = 8.27  # 修改为标准A4纸宽度 (inch)
FOCAL_LENGTH = 800  # 根据校准结果修改焦距 (pixels)
```

### 实时校准模式代码

如果需要实时调试，可以添加以下代码：

```python
# 添加到主循环中，用于实时调试
def debug_calibration_mode():
    """校准调试模式"""
    global FOCAL_LENGTH, KNOWN_WIDTH

    print("=== 校准模式 ===")
    print("当前参数:")
    print(f"KNOWN_WIDTH = {KNOWN_WIDTH}")
    print(f"FOCAL_LENGTH = {FOCAL_LENGTH}")

    # 可以通过串口或按键调整参数
    # 这里提供手动调整的框架

# 在main()函数中添加校准选项
if frame_count == 100:  # 启动后100帧显示校准信息
    debug_calibration_mode()
```

## 🎯 总结

**A4纸特征标记**就是A4纸的**黑色边框轮廓**，系统通过识别这个1.414长宽比的矩形边框来计算距离。

**距离不准确时，主要修改这两个参数**：
1. **KNOWN_WIDTH = 8.27** (A4纸标准宽度)
2. **FOCAL_LENGTH = 700-900** (根据实际校准调整)

按照本指南的校准步骤，您可以轻松获得高精度的距离测量结果！
