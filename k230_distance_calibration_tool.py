# K230距离测量校准工具
# Copyright: Mi Cu Electronics Studio
# Developer: <PERSON> (Product Manager) & <PERSON> (Engineer)
# 用于快速校准距离测量参数

import time
import os
import sys
import math
import image

# K230专用导入
try:
    from media.sensor import Sensor
    from media.display import Display
    from media.media import MediaManager
    from machine import Pin
    from machine import FPIOA
    print("✓ K230 media modules imported successfully")
except ImportError as e:
    print(f"❌ K230 import error: {e}")
    sys.exit(1)

# 校准参数 - 可以实时调整
PICTURE_WIDTH = 640
PICTURE_HEIGHT = 480

# 校准参数 - 这些是需要调整的关键参数
KNOWN_WIDTH = 10.31  # A4纸实际宽度 (英寸) - 用户已校准值
FOCAL_LENGTH = 501.7184  # 相机焦距 (像素) - 修正偏差：减小焦距 (551.8902 / 1.1)

# 校准测试距离 (厘米)
TEST_DISTANCES = [30, 50, 70, 100]  # 标准测试距离

# 按键配置 - 参考立创示例
CALIBRATION_BUTTON_PIN = 53  # 用户按键引脚 Bank4_GPIO53
CALIBRATION_GPIO_PIN = 53    # GPIO引脚号

# 全局变量
sensor = None
calibration_data = []
calibration_button = None

def distance_to_camera(knownWidth, focalLength, perWidth):
    """距离计算公式"""
    if perWidth <= 0:
        return 0
    return (knownWidth * focalLength) / perWidth

def find_a4_marker(img):
    """简化的A4纸检测"""
    try:
        # 检测黑色边框
        black_threshold = [(0, 50, -128, 127, -128, 127)]
        margin = 30
        search_roi = (margin, margin, PICTURE_WIDTH - 2*margin, PICTURE_HEIGHT - 2*margin)
        
        blobs = img.find_blobs(black_threshold, False, search_roi,
                              x_stride=2, y_stride=2,
                              pixels_threshold=500, margin=True)
        
        if len(blobs) == 0:
            return None
            
        # 筛选A4纸特征
        for blob in blobs:
            bw, bh = blob.w(), blob.h()
            area = blob.pixels()
            bbox_area = bw * bh
            fill_ratio = area / bbox_area if bbox_area > 0 else 0
            aspect_ratio = max(bw, bh) / min(bw, bh) if min(bw, bh) > 0 else 1.0
            
            # A4纸特征判断
            if (50 < bw < PICTURE_WIDTH * 0.8 and
                50 < bh < PICTURE_HEIGHT * 0.8 and
                2000 < area < PICTURE_WIDTH * PICTURE_HEIGHT * 0.6 and
                0.1 < fill_ratio < 0.6 and
                1.2 < aspect_ratio < 1.8):
                
                return {
                    'center': (blob.cx(), blob.cy()),
                    'size': (blob.w(), blob.h()),
                    'width_pixels': blob.w(),
                    'area': area,
                    'aspect_ratio': aspect_ratio
                }
        
        return None
        
    except Exception as e:
        print(f"检测错误: {e}")
        return None

def calculate_distance(marker, focal_length=None, known_width=None):
    """计算距离"""
    if not marker:
        return 0
        
    fl = focal_length if focal_length else FOCAL_LENGTH
    kw = known_width if known_width else KNOWN_WIDTH
    
    distance_inches = distance_to_camera(kw, fl, marker['width_pixels'])
    distance_cm = distance_inches * 2.54
    
    return distance_cm

def init_calibration_button():
    """初始化校准按键 - 参考立创示例"""
    global calibration_button
    try:
        # 第一步：创建FPIOA对象并分配引脚功能
        fpioa = FPIOA()
        fpioa.set_function(CALIBRATION_BUTTON_PIN, FPIOA.GPIO53)
        print(f"📌 FPIOA: Pin {CALIBRATION_BUTTON_PIN} -> GPIO53")

        # 第二步：初始化按键 - 立创示例使用PULL_DOWN，按下时高电平
        calibration_button = Pin(CALIBRATION_GPIO_PIN, Pin.IN, Pin.PULL_DOWN)
        print(f"🔘 Calibration button initialized: Pin {CALIBRATION_BUTTON_PIN} (Bank4_GPIO53)")
        print("🔍 Button mode: PULL_DOWN (0=released, 1=pressed)")

        # 测试按键初始状态
        initial_state = calibration_button.value()
        print(f"🔍 Button initial state: {initial_state}")

        return True
    except Exception as e:
        print(f"❌ Calibration button init failed: {e}")
        calibration_button = None
        return False

def check_calibration_button():
    """检查校准按键状态 - 参考立创示例"""
    global calibration_button
    if not calibration_button:
        return False

    try:
        current_state = calibration_button.value()
        return current_state == 1  # 按下时返回True
    except Exception as e:
        print(f"⚠️ Button check error: {e}")
        return False

def draw_calibration_info(img, marker, distance, focal_length, known_width):
    """绘制校准信息"""
    if marker:
        center = marker['center']
        size = marker['size']

        # 绘制标记
        half_w, half_h = size[0] // 2, size[1] // 2
        rect_x, rect_y = center[0] - half_w, center[1] - half_h
        img.draw_rectangle(rect_x, rect_y, size[0], size[1],
                          color=(0, 255, 0), thickness=3, fill=False)

        # 显示距离
        img.draw_string_advanced(10, 10, 20, f"Distance: {distance:.1f}cm", color=(0, 255, 0))
        img.draw_string_advanced(10, 35, 16, f"Pixels: {marker['width_pixels']}", color=(255, 255, 0))
        img.draw_string_advanced(10, 55, 16, f"Aspect: {marker['aspect_ratio']:.2f}", color=(255, 255, 0))

    # 显示当前参数
    img.draw_string_advanced(10, PICTURE_HEIGHT - 80, 16, f"KNOWN_WIDTH: {known_width:.2f}", color=(0, 255, 255))
    img.draw_string_advanced(10, PICTURE_HEIGHT - 60, 16, f"FOCAL_LENGTH: {focal_length}", color=(0, 255, 255))

    # 显示校准提示
    img.draw_string_advanced(10, PICTURE_HEIGHT - 40, 14, "Place A4 at known distance", color=(150, 150, 150))
    img.draw_string_advanced(10, PICTURE_HEIGHT - 20, 14, "Press GPIO53 to start calibration", color=(255, 255, 0))

def wait_for_button_press():
    """等待按键按下 - 替代input()"""
    print("🔘 等待按下GPIO53按键开始校准...")
    print("🔍 按键状态检测中...")

    # 初始化按键
    if not init_calibration_button():
        print("❌ 按键初始化失败，使用延时替代")
        time.sleep(3)
        return True

    # 等待按键按下
    last_state = 0
    while True:
        current_state = calibration_button.value() if calibration_button else 0

        # 检测上升沿（按键按下）
        if current_state == 1 and last_state == 0:
            print("🔘 按键按下！开始校准...")
            time.sleep_ms(200)  # 消抖延时
            return True

        last_state = current_state
        time.sleep_ms(50)  # 检测间隔

def calibration_mode():
    """校准模式主函数"""
    global sensor, FOCAL_LENGTH, KNOWN_WIDTH

    try:
        print("=== K230距离测量校准工具 ===")
        print("使用说明:")
        print("1. 将A4纸(黑色边框)放在已知距离处")
        print("2. 按下GPIO53按键开始校准")
        print("3. 记录显示的像素宽度")
        print("4. 使用公式计算新的焦距参数")
        print()

        # 等待按键按下
        wait_for_button_press()

        # 初始化硬件
        os.exitpoint(os.EXITPOINT_ENABLE)

        sensor = Sensor(width=PICTURE_WIDTH, height=PICTURE_HEIGHT)
        sensor.reset()
        sensor.set_framesize(width=PICTURE_WIDTH, height=PICTURE_HEIGHT)
        sensor.set_pixformat(Sensor.RGB565)

        Display.init(Display.ST7701, to_ide=True)
        MediaManager.init()
        sensor.run()

        print("硬件初始化完成")
        print(f"当前参数: KNOWN_WIDTH={KNOWN_WIDTH}, FOCAL_LENGTH={FOCAL_LENGTH}")
        print()
        
        frame_count = 0
        
        while True:
            os.exitpoint()
            
            try:
                img = sensor.snapshot()
                
                # 每3帧检测一次
                frame_count += 1
                if frame_count % 3 == 0:
                    marker = find_a4_marker(img)
                    
                    if marker:
                        distance = calculate_distance(marker, FOCAL_LENGTH, KNOWN_WIDTH)
                        
                        # 输出校准数据
                        print(f"检测数据: 像素宽度={marker['width_pixels']}, 计算距离={distance:.1f}cm")
                        
                        # 绘制信息
                        draw_calibration_info(img, marker, distance, FOCAL_LENGTH, KNOWN_WIDTH)
                        
                        # 显示校准公式
                        img.draw_string_advanced(PICTURE_WIDTH - 200, 10, 14, "Calibration Formula:", color=(255, 255, 0))
                        img.draw_string_advanced(PICTURE_WIDTH - 200, 30, 12, "FL = (KW * PW) / RD", color=(255, 255, 0))
                        img.draw_string_advanced(PICTURE_WIDTH - 200, 45, 12, f"FL = ({KNOWN_WIDTH} * {marker['width_pixels']}) / RealDist", color=(255, 255, 0))
                        
                    else:
                        img.draw_string_advanced(10, 10, 20, "SEARCHING A4 MARKER...", color=(255, 255, 0))
                        img.draw_string_advanced(10, 35, 16, "Place A4 with black border", color=(150, 150, 150))
                        
                        # 显示当前参数
                        img.draw_string_advanced(10, PICTURE_HEIGHT - 60, 16, f"KNOWN_WIDTH: {KNOWN_WIDTH:.2f}", color=(0, 255, 255))
                        img.draw_string_advanced(10, PICTURE_HEIGHT - 40, 16, f"FOCAL_LENGTH: {FOCAL_LENGTH}", color=(0, 255, 255))
                
                # 显示标题
                img.draw_string_advanced(10, PICTURE_HEIGHT - 100, 18, "K230 Calibration Tool", color=(0, 255, 255))
                
                Display.show_image(img)
                time.sleep_ms(100)
                
            except Exception as e:
                print(f"处理错误: {e}")
                time.sleep_ms(1000)
                
    except KeyboardInterrupt:
        print("校准工具停止")
    except Exception as e:
        print(f"系统错误: {e}")
    finally:
        try:
            if sensor:
                sensor.stop()
            Display.deinit()
            MediaManager.deinit()
        except:
            pass
        print("校准工具已退出")

def print_calibration_guide():
    """打印校准指南"""
    print("=" * 50)
    print("K230距离测量校准指南")
    print("=" * 50)
    print()
    print("步骤1: 准备A4纸标记")
    print("- 在A4纸边缘画黑色边框(5-10mm宽)")
    print("- 或者用黑色胶带贴边框")
    print("- 确保边框完整清晰")
    print()
    print("步骤2: 校准测试")
    print("- 将A4纸放在已知距离处(如50cm)")
    print("- 运行校准工具，记录像素宽度")
    print("- 使用公式计算新焦距:")
    print("  FOCAL_LENGTH = (KNOWN_WIDTH * 像素宽度) / 实际距离(英寸)")
    print()
    print("步骤3: 验证校准")
    print("- 在多个距离测试准确性")
    print("- 误差应控制在±5%以内")
    print()
    print("常用参数参考:")
    print("- KNOWN_WIDTH = 8.27 (A4纸标准宽度)")
    print("- FOCAL_LENGTH = 700-900 (根据相机调整)")
    print()
    print("=" * 50)

if __name__ == "__main__":
    print_calibration_guide()
    print("🔘 准备开始校准，请按下GPIO53按键...")
    calibration_mode()
